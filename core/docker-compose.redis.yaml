services:
  redis_onai:
    container_name: redis_onai
    hostname: redis_onai
    image: 'docker.dragonflydb.io/dragonflydb/dragonfly:v1.29.0'
    ulimits:
      memlock: -1
    cpus: 4
    networks:
      - caddy_net
    # command: [ "--default_lua_flags", "allow-undeclared-keys", "--cluster_mode", "emulated", "--lock_on_hashtags" ]
    volumes:
      - onai_redis:/data
    healthcheck:
      test: [ "CMD", "redis-cli", "ping" ]
      interval: 30s
      start_period: 20s
      timeout: 10s
      retries: 3
    restart: always
    stop_grace_period: 2m
    mem_limit: 5g

volumes:
  onai_redis:
