.PHONY: up down restart status logs help

ENV_FILE = .env

help:
	@echo "Available commands:"
	@echo "  make up      - Start all services (default)"
	@echo "  make down    - Stop all services"
	@echo "  make restart - Restart all services"
	@echo "  make status  - Show status of running containers"
	@echo "  make logs    - Show logs from all services"
	@echo "  make help    - Show this help message"

start:
	sudo docker compose --env-file $(ENV_FILE) up --remove-orphans

up:
	sudo docker compose --env-file $(ENV_FILE) up --remove-orphans

up-detached:
	sudo docker compose --env-file $(ENV_FILE) up -d --remove-orphans

down:
	sudo docker compose down

down-volumes:
	sudo docker compose down -v

restart: down up

status:
	sudo docker compose ps

logs:
	sudo docker compose logs -f

.DEFAULT_GOAL := up 