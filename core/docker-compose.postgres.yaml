x-default-pg-env: &default-pg-env
  TZ: Asia/Aqtau
  POSTGRESQL_USERNAME: ${POSTGRES_USER}
  POSTGRESQL_DATABASE: postgres
  POSTGRESQL_PASSWORD: ${POSTGRES_PASSWORD}
  
services:
  postgres_onai:
    image: postgres:16.3-alpine3.20
    container_name: postgres_onai
    hostname: postgres_onai
    networks:
      - caddy_net
    volumes:
      - onai_postgres:/var/lib/postgresql/data
    env_file:
      - .env
    environment:
      <<: *default-pg-env
    ports:
      - "5432:5432"
    healthcheck:
      test: [ "CMD-SHELL", "pg_isready -U postgres" ]
      interval: 5s
      timeout: 5s
      retries: 5
    stop_grace_period: 2m

  pgadmin4_onai:
    image: dpage/pgadmin4:9.5
    container_name: pgadmin4_onai
    hostname: pgadmin4_onai
    networks:
      - caddy_net
    ports:
        - 5433:80
    env_file:
      - .env
    restart: always
    tty: true

  # pgcat:
  #   image: ghcr.io/postgresml/pgcat
  #   container_name: pgcat
  #   restart: unless-stopped
  #   networks:
  #     - caddy_net
  #   environment:
  #     <<: *default-pg-env
  #   ports:
  #     - "6432:6432"
  #     - "9930:9930"
  #   depends_on:
  #     postgres_onai:
  #       condition: service_healthy
  #   volumes:
  #     - ./pgcat.toml:/etc/pgcat/pgcat.toml

volumes:
  onai_postgres:
