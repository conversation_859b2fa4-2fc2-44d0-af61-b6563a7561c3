import {
	WebTracerProvider,
	BatchSpanProcessor,
	ConsoleSpanExporter
} from '@opentelemetry/sdk-trace-web';
import { getWebAutoInstrumentations } from '@opentelemetry/auto-instrumentations-web';
import { OTLPTraceExporter } from '@opentelemetry/exporter-trace-otlp-http';
import { registerInstrumentations } from '@opentelemetry/instrumentation';
import { ZoneContextManager } from '@opentelemetry/context-zone';
import { FetchInstrumentation } from '@opentelemetry/instrumentation-fetch';
import { resourceFromAttributes } from '@opentelemetry/resources';
import { ATTR_SERVICE_NAME } from '@opentelemetry/semantic-conventions';
import { OTLPLogExporter } from '@opentelemetry/exporter-logs-otlp-proto';
import * as logsAPI from '@opentelemetry/api-logs';
import {
	LoggerProvider,
	BatchLogRecordProcessor,
	ConsoleLogRecordExporter
} from '@opentelemetry/sdk-logs';
import { onCLS, onINP, onLCP, onFCP, onTTFB, type Metric } from 'web-vitals';
import posthog from 'posthog-js';
import { publicEnv } from '@/config/public';

import { CLIENT_SERVICE_NAME as SERVICE_NAME } from '$lib/constants/telemetry';

const loggerExporter = new OTLPLogExporter({
	url: `http://${publicEnv.otlpTraceUrl}/purplish/v1/logs`
});

const traceExporter = new OTLPTraceExporter({
	url: `http://${publicEnv.otlpTraceUrl}/purplish/v1/traces`
});

const provider = new WebTracerProvider({
	resource: resourceFromAttributes({
		[ATTR_SERVICE_NAME]: SERVICE_NAME
	}),
	spanProcessors: [new BatchSpanProcessor(traceExporter)]
});

provider.register({
	contextManager: new ZoneContextManager()
});

registerInstrumentations({
	instrumentations: [
		new FetchInstrumentation({
			propagateTraceHeaderCorsUrls: [new RegExp(publicEnv.pythonApiBaseUrl, 'i')]
		})
	]
});

export function setupLoggerProvider() {
	const loggerProvider = new LoggerProvider({
		resource: resourceFromAttributes({
			[ATTR_SERVICE_NAME]: SERVICE_NAME
		}),
		processors: [
			new BatchLogRecordProcessor(loggerExporter),
			new BatchLogRecordProcessor(new ConsoleLogRecordExporter())
		]
	});
	logsAPI.logs.setGlobalLoggerProvider(loggerProvider);
	return loggerProvider;
}

export const logger = logsAPI.logs.getLogger(SERVICE_NAME);

export function startTelemetry() {
	setupLoggerProvider();

	const report = (metric: Metric) => {
		posthog.capture('web-vital', {
			name: metric.name,
			value: metric.value,
			delta: metric.delta,
			id: metric.id
		});
	};

	onCLS(report);
	onLCP(report);
	onINP(report);
	onFCP(report);
	onTTFB(report);

	getWebAutoInstrumentations();
	return {};
}
