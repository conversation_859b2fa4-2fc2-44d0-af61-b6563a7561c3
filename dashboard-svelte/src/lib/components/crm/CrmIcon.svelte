<script lang="ts">
	import { CrmType } from '$lib/types/crm.types';
	import onaiLogoUrl from '$lib/assets/logo.svg';
	import amocrmLogoUrl from '$lib/assets/amocrm-logo.svg';
	import bitrixLogoUrl from '$lib/assets/bitrix-logo.svg';
	import kommoLogoUrl from '$lib/assets/kommo-logo.svg';

	export let type: CrmType;
	export let size: 'sm' | 'md' | 'lg' = 'md';

	const sizeClasses = {
		sm: 'h-4 w-4',
		md: 'h-6 w-6',
		lg: 'h-8 w-8'
	};

	$: svelteClass = sizeClasses[size];
</script>

{#if type === CrmType.ONAI}
	<img src={onaiLogoUrl} class={svelteClass} alt="on ai logo" />
{:else if type === CrmType.AMOCRM}
	<img src={amocrmLogoUrl} class={svelteClass} alt="amocrm logo" />
{:else if type === CrmType.BITRIX}
	<img src={bitrixLogoUrl} class={svelteClass} alt="bitrix logo" />
{:else if type === CrmType.KOMMO}
	<img src={kommoLogoUrl} class={svelteClass} alt="kommo logo" />
{:else}{/if}
