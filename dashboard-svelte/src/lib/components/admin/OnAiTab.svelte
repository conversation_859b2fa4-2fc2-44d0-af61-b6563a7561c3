<script lang="ts">
	import { onai as onaiTitle } from '$lib/paraglide/messages';
	import { adminStore } from '$lib/stores/admin';
	import {
		Table,
		TableBody,
		TableCell,
		TableHead,
		TableHeader,
		TableRow
	} from '$lib/components/ui/table';
	import Skeleton from '$lib/components/ui/skeleton/skeleton.svelte';
	import ChevronLeftIcon from '@lucide/svelte/icons/chevron-left';
	import ChevronRightIcon from '@lucide/svelte/icons/chevron-right';
	import { MediaQuery } from 'svelte/reactivity';
	import * as Pagination from '$lib/components/ui/pagination/index.js';
	import { Badge } from '$lib/components/ui/badge';
	import { Input } from '$lib/components/ui/input';
	import * as Select from '$lib/components/ui/select';
	import { Button } from '$lib/components/ui/button';

	import { formatDate } from '$lib/utils/date';

	const isDesktop = new MediaQuery('(min-width: 768px)');
	let perPage = $derived(isDesktop.current ? 10 : 5);
	let siblingCount = $derived(isDesktop.current ? 1 : 0);
	let currentPage = $state(1);
	let searchQuery = $state('');
	let isActive: boolean | undefined = $state(undefined);

	$effect(() => {
		adminStore.fetchAllOnAis(currentPage, perPage, searchQuery, isActive);
	});

	function handleIsActiveChange(value: string | undefined) {
		if (value === 'true') {
			isActive = true;
		} else if (value === 'false') {
			isActive = false;
		} else {
			isActive = undefined;
		}
	}

	function handleSearch() {
		currentPage = 1;
		adminStore.fetchAllOnAis(currentPage, perPage, searchQuery, isActive);
	}

	function handleSubmit(e: SubmitEvent) {
		e.preventDefault();
		handleSearch();
	}
</script>

<h1 class="mb-4 text-2xl font-bold">{onaiTitle()}</h1>

<form onsubmit={handleSubmit} class="mb-4 flex items-center gap-4">
	<Input bind:value={searchQuery} placeholder="search..." class="w-full" />
	<Select.Root type="single" onValueChange={handleIsActiveChange}>
		<Select.Trigger class="w-[180px]">
			{isActive === undefined ? 'filter by status' : isActive ? 'active' : 'inactive'}
		</Select.Trigger>
		<Select.Content>
			<Select.Item value={'all'}>all</Select.Item>
			<Select.Item value={'true'}>active</Select.Item>
			<Select.Item value={'false'}>inactive</Select.Item>
		</Select.Content>
	</Select.Root>
	<Button type="submit" onclick={handleSearch}>search</Button>
</form>

{#if $adminStore.onAisLoading}
	<div class="space-y-2">
		{#each Array(perPage) as _}
			<Skeleton class="h-12 w-full" />
		{/each}
	</div>
{:else}
	<div class="rounded-md border">
		<Table>
			<TableHeader>
				<TableRow>
					<TableHead>name</TableHead>
					<TableHead>active</TableHead>
					<TableHead>workspace id</TableHead>
					<TableHead>created at</TableHead>
				</TableRow>
			</TableHeader>
			<TableBody>
				{#each $adminStore.onAis as onai, i (onai.id)}
					<TableRow class="fade-in" style="--animation-delay: {i * 50}ms;">
						<TableCell>{onai.name}</TableCell>
						<TableCell>
							<Badge variant={onai.is_active ? 'default' : 'destructive'}>
								{onai.is_active ? 'active' : 'inactive'}
							</Badge>
						</TableCell>
						<TableCell>{onai.workspace_id}</TableCell>
						<TableCell>{formatDate(new Date(onai.created_at))}</TableCell>
					</TableRow>
				{/each}
			</TableBody>
		</Table>
	</div>
	<div class="mt-4 flex justify-center">
		<Pagination.Root
			count={$adminStore.onAisTotal}
			{perPage}
			{siblingCount}
			bind:page={currentPage}
		>
			{#snippet children({ pages, currentPage })}
				<Pagination.Content>
					<Pagination.Item>
						<Pagination.PrevButton>
							<ChevronLeftIcon class="size-4" />
						</Pagination.PrevButton>
					</Pagination.Item>
					{#each pages as page (page.key)}
						{#if page.type === 'ellipsis'}
							<Pagination.Item>
								<Pagination.Ellipsis />
							</Pagination.Item>
						{:else}
							<Pagination.Item>
								<Pagination.Link {page} isActive={currentPage === page.value}>
									{page.value}
								</Pagination.Link>
							</Pagination.Item>
						{/if}
					{/each}
					<Pagination.Item>
						<Pagination.NextButton>
							<ChevronRightIcon class="size-4" />
						</Pagination.NextButton>
					</Pagination.Item>
				</Pagination.Content>
			{/snippet}
		</Pagination.Root>
	</div>
{/if}
