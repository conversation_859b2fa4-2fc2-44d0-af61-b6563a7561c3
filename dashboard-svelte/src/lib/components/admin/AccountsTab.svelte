<script lang="ts">
	import { accounts as accountsTitle } from '$lib/paraglide/messages';
	import { adminStore } from '$lib/stores/admin';
	import {
		Table,
		TableBody,
		TableCell,
		TableHead,
		TableHeader,
		TableRow
	} from '$lib/components/ui/table';
	import Skeleton from '$lib/components/ui/skeleton/skeleton.svelte';
	import ChevronLeftIcon from '@lucide/svelte/icons/chevron-left';
	import ChevronRightIcon from '@lucide/svelte/icons/chevron-right';
	import { MediaQuery } from 'svelte/reactivity';
	import * as Pagination from '$lib/components/ui/pagination/index.js';
	import { UserRole } from '$lib/types/auth.types';
	import { Input } from '$lib/components/ui/input';
	import * as Select from '$lib/components/ui/select';
	import { Button } from '$lib/components/ui/button';
	import { analytics } from '$lib/services/analytics';

	const isDesktop = new MediaQuery('(min-width: 768px)');
	let perPage = $derived(isDesktop.current ? 10 : 5);
	let siblingCount = $derived(isDesktop.current ? 1 : 0);
	let currentPage = $state(1);
	let searchQuery = $state('');
	let role: UserRole | undefined = $state(undefined);

	function handleSearch() {
		analytics.trackSearchPerformed(searchQuery, 'user_management', 0); // Count will be updated from results
		analytics.trackButtonClicked('search_users', 'accounts_tab', 'primary');
		currentPage = 1;
		adminStore.fetchUsers(currentPage, perPage, searchQuery, role);
	}

	function handleRoleChange(value: string | undefined) {
		analytics.trackDropdownUsed('role_filter', value || 'all', 'accounts_tab');
		analytics.trackFilterApplied('user_role', value || 'all', 'accounts_tab');
		role = value as UserRole | undefined;
	}

	function handleSubmit(e: SubmitEvent) {
		e.preventDefault();
		analytics.trackFormSubmitted('user_search', true);
		handleSearch();
	}
</script>

<h1 class="mb-4 text-2xl font-bold">{accountsTitle()}</h1>
<form onsubmit={handleSubmit} class="mb-4 flex items-center gap-4">
	<Input bind:value={searchQuery} placeholder="search..." class="w-full" />
	<Select.Root type="single" onValueChange={handleRoleChange}>
		<Select.Trigger class="w-[180px]">{role || 'filter by role'}</Select.Trigger>
		<Select.Content>
			<Select.Item value={UserRole.USER}>user</Select.Item>
			<Select.Item value={UserRole.ADMIN}>admin</Select.Item>
			<Select.Item value={UserRole.SUPERADMIN}>superadmin</Select.Item>
		</Select.Content>
	</Select.Root>
	<Button type="submit" onclick={handleSearch}>search</Button>
</form>

{#if $adminStore.usersLoading}
	<div class="space-y-2">
		{#each Array(perPage) as _}
			<Skeleton class="h-12 w-full" />
		{/each}
	</div>
{:else}
	<div class="rounded-md border">
		<Table>
			<TableHeader>
				<TableRow>
					<TableHead>email</TableHead>
					<TableHead>phone</TableHead>
					<TableHead>role</TableHead>
					<TableHead>balance</TableHead>
				</TableRow>
			</TableHeader>
			<TableBody>
				{#each $adminStore.users as user, i (user.id)}
					<TableRow class="fade-in" style="--animation-delay: {i * 50}ms;">
						<TableCell>{user.email || '-'}</TableCell>
						<TableCell>{user.phone_number || '-'}</TableCell>
						<TableCell>{user.role}</TableCell>
						<TableCell
							>{user.balance ? `${user.balance.amount} ${user.balance.currency}` : '-'}</TableCell
						>
					</TableRow>
				{/each}
			</TableBody>
		</Table>
	</div>
	<div class="mt-4 flex justify-center">
		<Pagination.Root
			count={$adminStore.usersTotal}
			{perPage}
			{siblingCount}
			bind:page={currentPage}
		>
			{#snippet children({ pages, currentPage })}
				<Pagination.Content>
					<Pagination.Item>
						<Pagination.PrevButton>
							<ChevronLeftIcon class="size-4" />
						</Pagination.PrevButton>
					</Pagination.Item>
					{#each pages as page (page.key)}
						{#if page.type === 'ellipsis'}
							<Pagination.Item>
								<Pagination.Ellipsis />
							</Pagination.Item>
						{:else}
							<Pagination.Item>
								<Pagination.Link {page} isActive={currentPage === page.value}>
									{page.value}
								</Pagination.Link>
							</Pagination.Item>
						{/if}
					{/each}
					<Pagination.Item>
						<Pagination.NextButton>
							<ChevronRightIcon class="size-4" />
						</Pagination.NextButton>
					</Pagination.Item>
				</Pagination.Content>
			{/snippet}
		</Pagination.Root>
	</div>
{/if}
