<script lang="ts">
	import { onMount } from 'svelte';
	import {
		Table,
		TableBody,
		TableCell,
		TableHead,
		TableHeader,
		TableRow
	} from '$lib/components/ui/table';
	import { Button } from '$lib/components/ui/button';
	import { Input } from '$lib/components/ui/input';
	import { Textarea } from '$lib/components/ui/textarea';
	import { Checkbox } from '$lib/components/ui/checkbox';
	import * as Dialog from '$lib/components/ui/dialog';
	import { Badge } from '$lib/components/ui/badge';
	import Skeleton from '$lib/components/ui/skeleton/skeleton.svelte';
	import { Plus, Edit, Trash2, Eye, EyeOff } from '@lucide/svelte';
	import CalendarIcon from '@lucide/svelte/icons/calendar';
	import { cn } from '$lib/utils.js';
	import { buttonVariants } from '$lib/components/ui/button/index.js';
	import { Calendar } from '$lib/components/ui/calendar/index.js';
	import * as Popover from '$lib/components/ui/popover/index.js';
	import {
		DateFormatter,
		type DateValue,
		getLocalTimeZone,
		parseDate
	} from '@internationalized/date';
	import type {
		SystemAlert,
		SystemAlertCreate,
		SystemAlertUpdate
	} from '$lib/types/system-alerts.types';
	import { adminSystemAlertsStore } from '$lib/stores/system-alerts-admin-store';
	import { analytics } from '$lib/services/analytics';

	let alerts = $derived($adminSystemAlertsStore.alerts);
	let loading = $derived($adminSystemAlertsStore.loading);
	let error = $derived($adminSystemAlertsStore.error);
	let totalCount = $derived($adminSystemAlertsStore.totalCount);
	let includeDeleted = $derived($adminSystemAlertsStore.includeDeleted);
	let currentPage = $derived($adminSystemAlertsStore.page);
	let perPage = $derived($adminSystemAlertsStore.pageSize);

	let isCreateDialogOpen = $state(false);
	let isEditDialogOpen = $state(false);
	let editingAlert: SystemAlert | null = $state(null);

	let form = $state({
		title: '',
		message: '',
		storage_key: '',
		is_active: true,
		priority: 0
	});

	const df = new DateFormatter('en-US', { dateStyle: 'long' });
	let startDateValue = $state<DateValue | undefined>();
	let endDateValue = $state<DateValue | undefined>();
	let startDateContentRef = $state<HTMLElement | null>(null);
	let endDateContentRef = $state<HTMLElement | null>(null);

	onMount(() => {
		adminSystemAlertsStore.load();
	});

	function resetForm() {
		form = {
			title: '',
			message: '',
			storage_key: '',
			is_active: true,
			priority: 0
		};
		startDateValue = undefined;
		endDateValue = undefined;
	}

	function openCreateDialog() {
		analytics.trackButtonClicked('create_system_alert', 'system_alerts_tab', 'primary');
		analytics.trackModalOpened('system_alert_dialog', 'creation');
		resetForm();
		isCreateDialogOpen = true;
	}

	function openEditDialog(alert: SystemAlert) {
		analytics.trackButtonClicked('edit_system_alert', 'system_alerts_tab', 'outline');
		analytics.trackModalOpened('system_alert_dialog', 'edit');
		editingAlert = alert;
		form = {
			title: alert.title,
			message: alert.message,
			storage_key: alert.storage_key,
			is_active: alert.is_active,
			priority: alert.priority
		};
		startDateValue = alert.start_date ? parseDate(alert.start_date.slice(0, 10)) : undefined;
		endDateValue = alert.end_date ? parseDate(alert.end_date.slice(0, 10)) : undefined;
		isEditDialogOpen = true;
	}

	function dateValueToIsoDate(value?: DateValue): string | null {
		if (!value) return null;
		const d = value.toDate(getLocalTimeZone());
		const year = d.getFullYear();
		const month = String(d.getMonth() + 1).padStart(2, '0');
		const day = String(d.getDate()).padStart(2, '0');
		return `${year}-${month}-${day}`;
	}

	async function handleCreate() {
		analytics.trackButtonClicked('submit_create_alert', 'system_alerts_tab', 'primary');

		const data: SystemAlertCreate = {
			title: form.title,
			message: form.message,
			storage_key: form.storage_key,
			is_active: form.is_active,
			priority: form.priority,
			start_date: dateValueToIsoDate(startDateValue),
			end_date: dateValueToIsoDate(endDateValue)
		};

		try {
			await adminSystemAlertsStore.create(data);
			analytics.trackFormSubmitted('create_system_alert', true);
			analytics.trackAdminSettingChanged('system_alert_created', data.title, null);
			isCreateDialogOpen = false;
			resetForm();
		} catch (error) {
			analytics.trackFormSubmitted('create_system_alert', false);
			analytics.trackErrorOccurred(
				'system_alert_create_failed',
				String(error),
				'system_alerts_tab',
				'medium'
			);
		}
	}

	async function handleUpdate() {
		if (!editingAlert) return;
		analytics.trackButtonClicked('submit_update_alert', 'system_alerts_tab', 'primary');

		const data: SystemAlertUpdate = {
			title: form.title,
			message: form.message,
			storage_key: form.storage_key,
			is_active: form.is_active,
			priority: form.priority,
			start_date: dateValueToIsoDate(startDateValue),
			end_date: dateValueToIsoDate(endDateValue)
		};

		try {
			await adminSystemAlertsStore.updateAlert(editingAlert.id, data);
			analytics.trackFormSubmitted('update_system_alert', true);
			analytics.trackAdminSettingChanged('system_alert_updated', data.title, editingAlert.title);
			isEditDialogOpen = false;
			editingAlert = null;
			resetForm();
		} catch (error) {
			analytics.trackFormSubmitted('update_system_alert', false);
			analytics.trackErrorOccurred(
				'system_alert_update_failed',
				String(error),
				'system_alerts_tab',
				'medium'
			);
		}
	}

	async function handleDelete(alert: SystemAlert) {
		if (!confirm(`are you sure you want to delete "${alert.title}"?`)) return;
		analytics.trackButtonClicked('delete_system_alert', 'system_alerts_tab', 'destructive');

		try {
			await adminSystemAlertsStore.deleteAlert(alert.id);
			analytics.trackFormSubmitted('delete_system_alert', true);
			analytics.trackAdminSettingChanged('system_alert_deleted', alert.title, null);
		} catch (error) {
			analytics.trackFormSubmitted('delete_system_alert', false);
			analytics.trackErrorOccurred(
				'system_alert_delete_failed',
				String(error),
				'system_alerts_tab',
				'medium'
			);
		}
	}

	async function toggleActive(alert: SystemAlert) {
		const newStatus = !alert.is_active;
		analytics.trackButtonClicked('toggle_alert_status', 'system_alerts_tab', 'outline');
		analytics.trackAdminSettingChanged('system_alert_status', newStatus, alert.is_active);

		try {
			await adminSystemAlertsStore.toggleActive(alert);
			analytics.trackFormSubmitted('toggle_alert_status', true);
		} catch (error) {
			analytics.trackFormSubmitted('toggle_alert_status', false);
			analytics.trackErrorOccurred(
				'alert_status_toggle_failed',
				String(error),
				'system_alerts_tab',
				'medium'
			);
		}
	}

	function formatDate(dateString: string | null | undefined) {
		if (!dateString) return '-';
		return new Date(dateString).toLocaleDateString();
	}

	function getPriorityColor(priority: number) {
		if (priority >= 80) return 'destructive';
		if (priority >= 50) return 'default';
		return 'secondary';
	}
</script>

<div class="p-6">
	<div class="mb-6 flex items-center justify-between">
		<h1 class="text-2xl font-bold">system alerts</h1>
		<Button onclick={openCreateDialog}>
			<Plus class="mr-2 h-4 w-4" />
			create alert
		</Button>
	</div>

	{#if error}
		<div class="mb-4 rounded-md border border-red-200 bg-red-50 p-4 text-red-800">
			{error}
		</div>
	{/if}

	<div class="mb-4 flex items-center gap-4">
		<div class="flex items-center space-x-2">
			<Checkbox
				id="include-deleted"
				bind:checked={includeDeleted}
				onclick={() => {
					adminSystemAlertsStore.setIncludeDeleted(!includeDeleted);
					adminSystemAlertsStore.load();
				}}
			/>
			<label for="include-deleted" class="text-sm">include deleted</label>
		</div>
		<Button variant="outline" onclick={() => adminSystemAlertsStore.load()}>refresh</Button>
	</div>

	{#if loading}
		<div class="space-y-2">
			{#each Array(perPage) as _}
				<Skeleton class="h-16 w-full" />
			{/each}
		</div>
	{:else if alerts.length === 0}
		<div class="text-muted-foreground rounded-md border border-dashed p-8 text-center">
			no alerts found
		</div>
	{:else}
		<div class="rounded-md border">
			<Table>
				<TableHeader>
					<TableRow>
						<TableHead>title</TableHead>
						<TableHead>storage key</TableHead>
						<TableHead>status</TableHead>
						<TableHead>priority</TableHead>
						<TableHead>dates</TableHead>
						<TableHead>created</TableHead>
						<TableHead class="text-right">actions</TableHead>
					</TableRow>
				</TableHeader>
				<TableBody>
					{#each alerts as alert (alert.id)}
						<TableRow>
							<TableCell class="font-medium">
								{alert.title}
								<div class="text-muted-foreground text-xs">
									{alert.message.slice(0, 100)}{alert.message.length > 100 ? '...' : ''}
								</div>
							</TableCell>
							<TableCell>
								<code class="text-xs">{alert.storage_key}</code>
							</TableCell>
							<TableCell>
								<Badge variant={alert.is_active ? 'default' : 'secondary'}>
									{alert.is_active ? 'active' : 'inactive'}
								</Badge>
							</TableCell>
							<TableCell>
								<Badge variant={getPriorityColor(alert.priority)}>
									{alert.priority}
								</Badge>
							</TableCell>
							<TableCell class="text-xs">
								<div>start: {formatDate(alert.start_date)}</div>
								<div>end: {formatDate(alert.end_date)}</div>
							</TableCell>
							<TableCell class="text-xs">
								{formatDate(alert.created_at)}
							</TableCell>
							<TableCell class="text-right">
								<div class="flex justify-end gap-2">
									<Button
										variant="ghost"
										size="sm"
										onclick={() => toggleActive(alert)}
										title={alert.is_active ? 'deactivate' : 'activate'}
									>
										{#if alert.is_active}
											<EyeOff class="h-4 w-4" />
										{:else}
											<Eye class="h-4 w-4" />
										{/if}
									</Button>
									<Button variant="ghost" size="sm" onclick={() => openEditDialog(alert)}>
										<Edit class="h-4 w-4" />
									</Button>
									<Button variant="ghost" size="sm" onclick={() => handleDelete(alert)}>
										<Trash2 class="h-4 w-4" />
									</Button>
								</div>
							</TableCell>
						</TableRow>
					{/each}
				</TableBody>
			</Table>
		</div>
	{/if}

	<Dialog.Root bind:open={isCreateDialogOpen}>
		<Dialog.Content class="max-w-2xl">
			<Dialog.Header>
				<Dialog.Title>create system alert</Dialog.Title>
			</Dialog.Header>
			<div class="grid gap-4 py-4">
				<div class="grid gap-2">
					<label for="title" class="text-sm font-medium">title</label>
					<Input id="title" bind:value={form.title} placeholder="system alert" />
				</div>
				<div class="grid gap-2">
					<label for="message" class="text-sm font-medium">message</label>
					<Textarea
						id="message"
						bind:value={form.message}
						placeholder="we are experiencing issues..."
					/>
				</div>
				<div class="grid gap-2">
					<label for="storage_key" class="text-sm font-medium">storage key</label>
					<Input
						id="storage_key"
						bind:value={form.storage_key}
						placeholder="system-alert-2024-01"
					/>
				</div>
				<div class="grid grid-cols-2 gap-4">
					<div class="grid gap-2">
						<label for="start-date" class="text-sm font-medium">start date (optional)</label>
						<Popover.Root>
							<Popover.Trigger
								id="start-date"
								class={cn(
									buttonVariants({
										variant: 'outline',
										class: 'w-[280px] justify-start text-left font-normal'
									}),
									!startDateValue && 'text-muted-foreground'
								)}
							>
								<CalendarIcon />
								{startDateValue
									? df.format(startDateValue.toDate(getLocalTimeZone()))
									: 'pick a date'}
							</Popover.Trigger>
							<Popover.Content bind:ref={startDateContentRef} class="w-auto p-0">
								<Calendar type="single" bind:value={startDateValue} />
							</Popover.Content>
						</Popover.Root>
					</div>
					<div class="grid gap-2">
						<label for="end-date" class="text-sm font-medium">end date (optional)</label>
						<Popover.Root>
							<Popover.Trigger
								id="end-date"
								class={cn(
									buttonVariants({
										variant: 'outline',
										class: 'w-[280px] justify-start text-left font-normal'
									}),
									!endDateValue && 'text-muted-foreground'
								)}
							>
								<CalendarIcon />
								{endDateValue ? df.format(endDateValue.toDate(getLocalTimeZone())) : 'pick a date'}
							</Popover.Trigger>
							<Popover.Content bind:ref={endDateContentRef} class="w-auto p-0">
								<Calendar type="single" bind:value={endDateValue} />
							</Popover.Content>
						</Popover.Root>
					</div>
				</div>
			</div>
			<Dialog.Footer>
				<Button variant="outline" onclick={() => (isCreateDialogOpen = false)}>cancel</Button>
				<Button onclick={handleCreate}>create alert</Button>
			</Dialog.Footer>
		</Dialog.Content>
	</Dialog.Root>

	<Dialog.Root bind:open={isEditDialogOpen}>
		<Dialog.Content class="max-w-2xl">
			<Dialog.Header>
				<Dialog.Title>edit system alert</Dialog.Title>
			</Dialog.Header>
			<div class="grid gap-4 py-4">
				<div class="grid gap-2">
					<label for="edit_title" class="text-sm font-medium">title</label>
					<Input id="edit_title" bind:value={form.title} placeholder="system alert" />
				</div>
				<div class="grid gap-2">
					<label for="edit_message" class="text-sm font-medium">message</label>
					<Textarea
						id="edit_message"
						bind:value={form.message}
						placeholder="we are experiencing issues..."
					/>
				</div>
				<div class="grid gap-2">
					<label for="edit_storage_key" class="text-sm font-medium">storage key</label>
					<Input
						id="edit_storage_key"
						bind:value={form.storage_key}
						placeholder="system-alert-2024-01"
					/>
				</div>
				<div class="grid grid-cols-2 gap-4">
					<div class="grid gap-2">
						<label for="edit-start-date" class="text-sm font-medium">start date (optional)</label>
						<Popover.Root>
							<Popover.Trigger
								id="edit-start-date"
								class={cn(
									buttonVariants({
										variant: 'outline',
										class: 'w-[280px] justify-start text-left font-normal'
									}),
									!startDateValue && 'text-muted-foreground'
								)}
							>
								<CalendarIcon />
								{startDateValue
									? df.format(startDateValue.toDate(getLocalTimeZone()))
									: 'pick a date'}
							</Popover.Trigger>
							<Popover.Content bind:ref={startDateContentRef} class="w-auto p-0">
								<Calendar type="single" bind:value={startDateValue} />
							</Popover.Content>
						</Popover.Root>
					</div>
					<div class="grid gap-2">
						<label for="edit-end-date" class="text-sm font-medium">end date (optional)</label>
						<Popover.Root>
							<Popover.Trigger
								id="edit-end-date"
								class={cn(
									buttonVariants({
										variant: 'outline',
										class: 'w-[280px] justify-start text-left font-normal'
									}),
									!endDateValue && 'text-muted-foreground'
								)}
							>
								<CalendarIcon />
								{endDateValue ? df.format(endDateValue.toDate(getLocalTimeZone())) : 'pick a date'}
							</Popover.Trigger>
							<Popover.Content bind:ref={endDateContentRef} class="w-auto p-0">
								<Calendar type="single" bind:value={endDateValue} />
							</Popover.Content>
						</Popover.Root>
					</div>
				</div>
			</div>
			<Dialog.Footer>
				<Button variant="outline" onclick={() => (isEditDialogOpen = false)}>cancel</Button>
				<Button onclick={handleUpdate}>update alert</Button>
			</Dialog.Footer>
		</Dialog.Content>
	</Dialog.Root>
</div>
