<script lang="ts">
	import { onMount } from 'svelte';
	import { But<PERSON> } from '$lib/components/ui/button';
	import PromptDialog from '$lib/components/dashboard/PromptDialog.svelte';
	import { adminPromptStore } from '$lib/stores/admin-prompt-store';
	import { PROMPT_MAX_CHARS } from '$lib/constants/prompt';
	import * as Accordion from '$lib/components/ui/accordion';
	import {
		prompts as promptsTitle,
		onai_edit_base,
		onai_update_base,
		onai_base,
		onai_enter_base,
		onai_base_updated,
		onai_edit_reminder,
		onai_update_reminder,
		onai_reminder,
		onai_enter_reminder,
		onai_reminder_updated,
		onai_edit_report,
		onai_update_report,
		onai_report,
		onai_enter_report,
		onai_report_updated,
		main_prompt,
		report_prompt,
		system_prompt,
		user_prompt,
		onai_edit_media_description,
		onai_update_media_description,
		onai_media_description,
		onai_enter_media_description,
		onai_media_description_updated,
		onai_edit_document_summary,
		onai_update_document_summary,
		onai_document_summary,
		onai_enter_document_summary,
		onai_document_summary_updated,
		language_detection_system_prompt,
		onai_edit_chat_language_detection,
		onai_update_chat_language_detection,
		onai_chat_language_detection,
		onai_enter_chat_language_detection,
		onai_chat_language_detection_updated
	} from '$lib/paraglide/messages';

	let showMainPromptDialog = false;

	let showReportPromptDialog = false;
	let showImageSystemDescriptionDialog = false;
	let showImageUserDescriptionDialog = false;
	let showDocumentSystemSummaryDialog = false;
	let showDocumentUserSummaryDialog = false;
	let showStickerSystemDescriptionDialog = false;
	let showStickerUserDescriptionDialog = false;
	let showVideoSystemDescriptionDialog = false;
	let showVideoUserDescriptionDialog = false;
	let showChatLanguageDetectionDialog = false;
	let formError: string | undefined = undefined;

	onMount(() => {
		adminPromptStore.loadPrompt('main');
		adminPromptStore.loadPrompt('report');
		adminPromptStore.loadPrompt('image_system_description');
		adminPromptStore.loadPrompt('image_user_description');
		adminPromptStore.loadPrompt('document_system_summary');
		adminPromptStore.loadPrompt('document_user_summary');
		adminPromptStore.loadPrompt('sticker_system_description');
		adminPromptStore.loadPrompt('sticker_user_description');
		adminPromptStore.loadPrompt('video_system_description');
		adminPromptStore.loadPrompt('video_user_description');
		adminPromptStore.loadPrompt('chat_language_detection');
	});
</script>

<h1 class="text-2xl font-bold">{promptsTitle()}</h1>

<div class="mt-4">
	<Accordion.Root type="multiple" class="w-full">
		<!-- Main Prompts -->
		<Accordion.Item value="main-prompts">
			<Accordion.Trigger class="text-lg font-medium">main prompts</Accordion.Trigger>
			<Accordion.Content>
				<div class="flex flex-wrap gap-2 p-2">
					<Button variant="default" onclick={() => (showMainPromptDialog = true)}>
						{main_prompt()}
					</Button>
					<Button variant="default" onclick={() => (showReportPromptDialog = true)}>
						{report_prompt()}
					</Button>
				</div>
			</Accordion.Content>
		</Accordion.Item>

		<!-- Image Prompts -->
		<Accordion.Item value="image-prompts">
			<Accordion.Trigger class="text-lg font-medium">image prompts</Accordion.Trigger>
			<Accordion.Content>
				<div class="flex flex-wrap gap-2 p-2">
					<Button variant="default" onclick={() => (showImageSystemDescriptionDialog = true)}>
						{system_prompt()}
					</Button>
					<Button variant="default" onclick={() => (showImageUserDescriptionDialog = true)}>
						{user_prompt()}
					</Button>
				</div>
			</Accordion.Content>
		</Accordion.Item>

		<!-- Document Prompts -->
		<Accordion.Item value="document-prompts">
			<Accordion.Trigger class="text-lg font-medium">document prompts</Accordion.Trigger>
			<Accordion.Content>
				<div class="flex flex-wrap gap-2 p-2">
					<Button variant="default" onclick={() => (showDocumentSystemSummaryDialog = true)}>
						{system_prompt()}
					</Button>
					<Button variant="default" onclick={() => (showDocumentUserSummaryDialog = true)}>
						{user_prompt()}
					</Button>
				</div>
			</Accordion.Content>
		</Accordion.Item>

		<!-- Sticker Prompts -->
		<Accordion.Item value="sticker-prompts">
			<Accordion.Trigger class="text-lg font-medium">sticker prompts</Accordion.Trigger>
			<Accordion.Content>
				<div class="flex flex-wrap gap-2 p-2">
					<Button variant="default" onclick={() => (showStickerSystemDescriptionDialog = true)}>
						{system_prompt()}
					</Button>
					<Button variant="default" onclick={() => (showStickerUserDescriptionDialog = true)}>
						{user_prompt()}
					</Button>
				</div>
			</Accordion.Content>
		</Accordion.Item>

		<!-- Video Prompts -->
		<Accordion.Item value="video-prompts">
			<Accordion.Trigger class="text-lg font-medium">video prompts</Accordion.Trigger>
			<Accordion.Content>
				<div class="flex flex-wrap gap-2 p-2">
					<Button variant="default" onclick={() => (showVideoSystemDescriptionDialog = true)}>
						{system_prompt()}
					</Button>
					<Button variant="default" onclick={() => (showVideoUserDescriptionDialog = true)}>
						{user_prompt()}
					</Button>
				</div>
			</Accordion.Content>
		</Accordion.Item>

		<!-- Voice Prompts -->
		<Accordion.Item value="voice-prompts">
			<Accordion.Trigger class="text-lg font-medium">voice prompts</Accordion.Trigger>
			<Accordion.Content>
				<div class="flex flex-wrap gap-2 p-2">
					<Button variant="default" onclick={() => (showChatLanguageDetectionDialog = true)}>
						{language_detection_system_prompt()}
					</Button>
				</div>
			</Accordion.Content>
		</Accordion.Item>
	</Accordion.Root>
</div>

<PromptDialog
	bind:open={showMainPromptDialog}
	type="main"
	title={onai_edit_base()}
	description={onai_update_base()}
	label={onai_base()}
	placeholder={onai_enter_base()}
	maxLength={PROMPT_MAX_CHARS}
	successMessage={onai_base_updated()}
	store={adminPromptStore}
	{formError}
	on:close={() => (showMainPromptDialog = false)}
/>

<PromptDialog
	bind:open={showReportPromptDialog}
	type="report"
	title={onai_edit_report()}
	description={onai_update_report()}
	label={onai_report()}
	placeholder={onai_enter_report()}
	maxLength={PROMPT_MAX_CHARS}
	successMessage={onai_report_updated()}
	store={adminPromptStore}
	{formError}
	on:close={() => (showReportPromptDialog = false)}
/>

<PromptDialog
	bind:open={showImageSystemDescriptionDialog}
	type="image_system_description"
	title={onai_edit_media_description({ media: 'image', role: 'system' })}
	description={onai_update_media_description({ media: 'image', role: 'system' })}
	label={onai_media_description({ media: 'image', role: 'system' })}
	placeholder={onai_enter_media_description({ media: 'image', role: 'system' })}
	maxLength={PROMPT_MAX_CHARS}
	successMessage={onai_media_description_updated({ media: 'image', role: 'system' })}
	store={adminPromptStore}
	{formError}
	on:close={() => (showImageSystemDescriptionDialog = false)}
/>

<PromptDialog
	bind:open={showImageUserDescriptionDialog}
	type="image_user_description"
	title={onai_edit_media_description({ media: 'image', role: 'user' })}
	description={onai_update_media_description({ media: 'image', role: 'user' })}
	label={onai_media_description({ media: 'image', role: 'user' })}
	placeholder={onai_enter_media_description({ media: 'image', role: 'user' })}
	maxLength={PROMPT_MAX_CHARS}
	successMessage={onai_media_description_updated({ media: 'image', role: 'user' })}
	store={adminPromptStore}
	{formError}
	on:close={() => (showImageUserDescriptionDialog = false)}
/>

<PromptDialog
	bind:open={showDocumentSystemSummaryDialog}
	type="document_system_summary"
	title={onai_edit_document_summary({ role: 'system' })}
	description={onai_update_document_summary({ role: 'system' })}
	label={onai_document_summary({ role: 'system' })}
	placeholder={onai_enter_document_summary({ role: 'system' })}
	maxLength={PROMPT_MAX_CHARS}
	successMessage={onai_document_summary_updated({ role: 'system' })}
	store={adminPromptStore}
	{formError}
	on:close={() => (showDocumentSystemSummaryDialog = false)}
/>

<PromptDialog
	bind:open={showDocumentUserSummaryDialog}
	type="document_user_summary"
	title={onai_edit_document_summary({ role: 'user' })}
	description={onai_update_document_summary({ role: 'user' })}
	label={onai_document_summary({ role: 'user' })}
	placeholder={onai_enter_document_summary({ role: 'user' })}
	maxLength={PROMPT_MAX_CHARS}
	successMessage={onai_document_summary_updated({ role: 'user' })}
	store={adminPromptStore}
	{formError}
	on:close={() => (showDocumentUserSummaryDialog = false)}
/>

<PromptDialog
	bind:open={showStickerSystemDescriptionDialog}
	type="sticker_system_description"
	title={onai_edit_media_description({ media: 'sticker', role: 'system' })}
	description={onai_update_media_description({ media: 'sticker', role: 'system' })}
	label={onai_media_description({ media: 'sticker', role: 'system' })}
	placeholder={onai_enter_media_description({ media: 'sticker', role: 'system' })}
	maxLength={PROMPT_MAX_CHARS}
	successMessage={onai_media_description_updated({ media: 'sticker', role: 'system' })}
	store={adminPromptStore}
	{formError}
	on:close={() => (showStickerSystemDescriptionDialog = false)}
/>

<PromptDialog
	bind:open={showStickerUserDescriptionDialog}
	type="sticker_user_description"
	title={onai_edit_media_description({ media: 'sticker', role: 'user' })}
	description={onai_update_media_description({ media: 'sticker', role: 'user' })}
	label={onai_media_description({ media: 'sticker', role: 'user' })}
	placeholder={onai_enter_media_description({ media: 'sticker', role: 'user' })}
	maxLength={PROMPT_MAX_CHARS}
	successMessage={onai_media_description_updated({ media: 'sticker', role: 'user' })}
	store={adminPromptStore}
	{formError}
	on:close={() => (showStickerUserDescriptionDialog = false)}
/>

<PromptDialog
	bind:open={showVideoSystemDescriptionDialog}
	type="video_system_description"
	title={onai_edit_media_description({ media: 'video', role: 'system' })}
	description={onai_update_media_description({ media: 'video', role: 'system' })}
	label={onai_media_description({ media: 'video', role: 'system' })}
	placeholder={onai_enter_media_description({ media: 'video', role: 'system' })}
	maxLength={PROMPT_MAX_CHARS}
	successMessage={onai_media_description_updated({ media: 'video', role: 'system' })}
	store={adminPromptStore}
	{formError}
	on:close={() => (showVideoSystemDescriptionDialog = false)}
/>

<PromptDialog
	bind:open={showVideoUserDescriptionDialog}
	type="video_user_description"
	title={onai_edit_media_description({ media: 'video', role: 'user' })}
	description={onai_update_media_description({ media: 'video', role: 'user' })}
	label={onai_media_description({ media: 'video', role: 'user' })}
	placeholder={onai_enter_media_description({ media: 'video', role: 'user' })}
	maxLength={PROMPT_MAX_CHARS}
	successMessage={onai_media_description_updated({ media: 'video', role: 'user' })}
	store={adminPromptStore}
	{formError}
	on:close={() => (showVideoUserDescriptionDialog = false)}
/>

<PromptDialog
	bind:open={showChatLanguageDetectionDialog}
	type="chat_language_detection"
	title={onai_edit_chat_language_detection()}
	description={onai_update_chat_language_detection()}
	label={onai_chat_language_detection()}
	placeholder={onai_enter_chat_language_detection()}
	maxLength={PROMPT_MAX_CHARS}
	successMessage={onai_chat_language_detection_updated()}
	store={adminPromptStore}
	{formError}
	on:close={() => (showChatLanguageDetectionDialog = false)}
/>
