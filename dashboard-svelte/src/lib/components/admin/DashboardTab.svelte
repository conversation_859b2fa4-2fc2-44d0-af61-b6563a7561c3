<script lang="ts">
	import { adminStore } from '$lib/stores/admin';
	import { onMount, onDestroy } from 'svelte';
	import Skeleton from '$lib/components/ui/skeleton/skeleton.svelte';
	// @ts-ignore - Ignore paraglide import errors as requested
	import {
		dashboard,
		total_accounts,
		total_onai,
		total_processed_messages,
		daily_processed_messages
	} from '$lib/paraglide/messages';
	import {
		Chart,
		LineElement,
		PointElement,
		LineController,
		CategoryScale,
		LinearScale,
		Title,
		Tooltip,
		Legend,
		Filler
	} from 'chart.js';

	// Register Chart.js components
	Chart.register(
		LineElement,
		PointElement,
		LineController,
		CategoryScale,
		LinearScale,
		Title,
		Tooltip,
		Legend,
		Filler
	);

	let accountsChartCanvas: HTMLCanvasElement;
	let onAiChartCanvas: HTMLCanvasElement;
	let messagesChartCanvas: HTMLCanvasElement;
	let accountsChart: Chart | null = null;
	let onAiChart: Chart | null = null;
	let messagesChart: Chart | null = null;

	onMount(() => {
		// Fetch current counts
		adminStore.fetchAccountsCount();
		adminStore.fetchOnAisCount();
		adminStore.fetchProcessedMessagesCount();

		// Fetch daily metrics for charts
		adminStore.fetchAccountsDailyMetrics(30);
		adminStore.fetchOnAiDailyMetrics(30);
		adminStore.fetchProcessedMessagesDailyMetrics(30);

		return () => {};
	});

	onDestroy(() => {
		if (accountsChart) {
			accountsChart.destroy();
		}
		if (onAiChart) {
			onAiChart.destroy();
		}
		if (messagesChart) {
			messagesChart.destroy();
		}
	});

	// Create charts when data is available
	$: if ($adminStore.accountsDailyMetrics.length > 0 && accountsChartCanvas && !accountsChart) {
		createAccountsChart();
	}

	$: if ($adminStore.onAiDailyMetrics.length > 0 && onAiChartCanvas && !onAiChart) {
		createOnAiChart();
	}

	$: if (
		$adminStore.processedMessagesDailyMetrics.length > 0 &&
		messagesChartCanvas &&
		!messagesChart
	) {
		createMessagesChart();
	}

	function createAccountsChart() {
		const ctx = accountsChartCanvas.getContext('2d');
		if (!ctx) return;

		const labels = $adminStore.accountsDailyMetrics.map((item) => {
			const date = new Date(item.date);
			return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
		});
		const data = $adminStore.accountsDailyMetrics.map((item) => item.count);

		accountsChart = new Chart(ctx, {
			type: 'line',
			data: {
				labels,
				datasets: [
					{
						label: 'accounts created',
						data,
						borderColor: 'rgb(59, 130, 246)',
						backgroundColor: 'rgba(59, 130, 246, 0.1)',
						fill: true,
						tension: 0.3,
						pointRadius: 3,
						pointHoverRadius: 6
					}
				]
			},
			options: {
				responsive: true,
				maintainAspectRatio: false,
				plugins: {
					legend: {
						display: false
					},
					tooltip: {
						mode: 'index',
						intersect: false
					}
				},
				scales: {
					x: {
						display: true,
						grid: {
							display: false
						},
						ticks: {
							font: {
								size: 11
							}
						}
					},
					y: {
						display: true,
						beginAtZero: true,
						grid: {
							color: 'rgba(0, 0, 0, 0.1)'
						},
						ticks: {
							font: {
								size: 11
							},
							stepSize: 1
						}
					}
				},
				interaction: {
					mode: 'nearest',
					axis: 'x',
					intersect: false
				}
			}
		});
	}

	function createOnAiChart() {
		const ctx = onAiChartCanvas.getContext('2d');
		if (!ctx) return;

		const labels = $adminStore.onAiDailyMetrics.map((item) => {
			const date = new Date(item.date);
			return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
		});
		const data = $adminStore.onAiDailyMetrics.map((item) => item.count);

		onAiChart = new Chart(ctx, {
			type: 'line',
			data: {
				labels,
				datasets: [
					{
						label: 'on.ai created',
						data,
						borderColor: 'rgb(16, 185, 129)',
						backgroundColor: 'rgba(16, 185, 129, 0.1)',
						fill: true,
						tension: 0.3,
						pointRadius: 3,
						pointHoverRadius: 6
					}
				]
			},
			options: {
				responsive: true,
				maintainAspectRatio: false,
				plugins: {
					legend: {
						display: false
					},
					tooltip: {
						mode: 'index',
						intersect: false
					}
				},
				scales: {
					x: {
						display: true,
						grid: {
							display: false
						},
						ticks: {
							font: {
								size: 11
							}
						}
					},
					y: {
						display: true,
						beginAtZero: true,
						grid: {
							color: 'rgba(0, 0, 0, 0.1)'
						},
						ticks: {
							font: {
								size: 11
							},
							stepSize: 1
						}
					}
				},
				interaction: {
					mode: 'nearest',
					axis: 'x',
					intersect: false
				}
			}
		});
	}

	function createMessagesChart() {
		const ctx = messagesChartCanvas.getContext('2d');
		if (!ctx) return;

		const labels = $adminStore.processedMessagesDailyMetrics.map((item) => {
			const date = new Date(item.date);
			return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
		});
		const data = $adminStore.processedMessagesDailyMetrics.map((item) => item.count);

		messagesChart = new Chart(ctx, {
			type: 'line',
			data: {
				labels,
				datasets: [
					{
						label: 'messages processed',
						data,
						borderColor: 'rgb(139, 69, 19)',
						backgroundColor: 'rgba(139, 69, 19, 0.1)',
						fill: true,
						tension: 0.3,
						pointRadius: 3,
						pointHoverRadius: 6
					}
				]
			},
			options: {
				responsive: true,
				maintainAspectRatio: false,
				plugins: {
					legend: {
						display: false
					},
					tooltip: {
						mode: 'index',
						intersect: false
					}
				},
				scales: {
					x: {
						display: true,
						grid: {
							display: false
						},
						ticks: {
							font: {
								size: 11
							}
						}
					},
					y: {
						display: true,
						beginAtZero: true,
						grid: {
							color: 'rgba(0, 0, 0, 0.1)'
						},
						ticks: {
							font: {
								size: 11
							},
							stepSize: 1
						}
					}
				},
				interaction: {
					mode: 'nearest',
					axis: 'x',
					intersect: false
				}
			}
		});
	}
</script>

<h1 class="text-2xl font-bold dark:text-white">{dashboard()}</h1>

<div class="container mx-auto p-8">
	<div class="grid grid-cols-1 gap-6 lg:grid-cols-3">
		<!-- Accounts Chart -->
		<div
			class="slide-up fade-in rounded-lg bg-white p-6 shadow dark:bg-gray-800 dark:shadow-gray-700"
		>
			{#if $adminStore.loading || $adminStore.accountsDailyMetricsLoading}
				<div class="w-full space-y-2">
					<Skeleton class="h-6 w-3/4" />
					<Skeleton class="h-8 w-1/4" />
					<Skeleton class="mt-4 h-64 w-full" />
				</div>
			{:else}
				<div class="mb-4">
					<h3 class="text-lg font-semibold dark:text-gray-200">{total_accounts()}</h3>
					<p class="text-2xl font-bold dark:text-white">{$adminStore.accountsCount}</p>
					<p class="text-sm text-gray-500 dark:text-gray-400">daily creation over 30 days</p>
				</div>
				<div class="h-64 w-full">
					<canvas bind:this={accountsChartCanvas} class="h-full w-full"></canvas>
				</div>
			{/if}
		</div>

		<!-- OnAI Chart -->
		<div
			class="slide-up fade-in rounded-lg bg-white p-6 shadow dark:bg-gray-800 dark:shadow-gray-700"
			style="--animation-delay: 100ms;"
		>
			{#if $adminStore.onAisLoading || $adminStore.onAiDailyMetricsLoading}
				<div class="w-full space-y-2">
					<Skeleton class="h-6 w-3/4" />
					<Skeleton class="h-8 w-1/4" />
					<Skeleton class="mt-4 h-64 w-full" />
				</div>
			{:else}
				<div class="mb-4">
					<h3 class="text-lg font-semibold dark:text-gray-200">{total_onai()}</h3>
					<p class="text-2xl font-bold dark:text-white">{$adminStore.onAisTotal}</p>
					<p class="text-sm text-gray-500 dark:text-gray-400">daily creation over 30 days</p>
				</div>
				<div class="h-64 w-full">
					<canvas bind:this={onAiChartCanvas} class="h-full w-full"></canvas>
				</div>
			{/if}
		</div>

		<!-- Processed Messages Chart -->
		<div
			class="slide-up fade-in rounded-lg bg-white p-6 shadow dark:bg-gray-800 dark:shadow-gray-700"
			style="--animation-delay: 200ms;"
		>
			{#if $adminStore.processedMessagesLoading || $adminStore.processedMessagesDailyMetricsLoading}
				<div class="w-full space-y-2">
					<Skeleton class="h-6 w-3/4" />
					<Skeleton class="h-8 w-1/4" />
					<Skeleton class="mt-4 h-64 w-full" />
				</div>
			{:else}
				<div class="mb-4">
					<h3 class="text-lg font-semibold dark:text-gray-200">{total_processed_messages()}</h3>
					<p class="text-2xl font-bold dark:text-white">{$adminStore.processedMessagesTotal}</p>
					<p class="text-sm text-gray-500 dark:text-gray-400">{daily_processed_messages()}</p>
				</div>
				<div class="h-64 w-full">
					<canvas bind:this={messagesChartCanvas} class="h-full w-full"></canvas>
				</div>
			{/if}
		</div>
	</div>
</div>
