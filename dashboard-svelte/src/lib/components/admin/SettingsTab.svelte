<script lang="ts">
	import { Checkbox } from '$lib/components/ui/checkbox';
	import { llmSettingsStore } from '$lib/stores/llm-settings-store';
	import { analytics } from '$lib/services/analytics';
	import { onMount } from 'svelte';

	let anthropicFallback = $derived($llmSettingsStore.anthropicFallback);

	onMount(() => {
		llmSettingsStore.initialize();
	});

	function handleChange() {
		const oldValue = $llmSettingsStore.anthropicFallback;
		const newValue = anthropicFallback;

		// Track admin setting change
		analytics.trackAdminSettingChanged('anthropic_fallback_to_openrouter', newValue, oldValue);
		analytics.trackSettingChanged(
			'anthropic_fallback_to_openrouter',
			newValue,
			oldValue,
			'llm_settings'
		);

		llmSettingsStore.setAnthropicFallback(anthropicFallback);
	}
</script>

<div class="p-4">
	<div class="flex items-center space-x-2">
		<Checkbox
			id="anthropic-fallback"
			bind:checked={anthropicFallback}
			onclick={handleChange}
			data-ph-capture-attribute-setting="anthropic-fallback"
			data-ph-capture-attribute-action="admin-setting-change"
			data-ph-capture-attribute-setting-enabled={anthropicFallback}
		/>
		<label for="anthropic-fallback" class="text-sm">anthropic: fallback to openrouter</label>
	</div>
</div>
