<script lang="ts">
	import { Button } from '$lib/components/ui/button';
	import * as InputOTP from '$lib/components/ui/input-otp/index.js';
	import { REGEXP_ONLY_DIGITS } from 'bits-ui';
	import { createEventDispatcher } from 'svelte';
	import { debounce } from 'es-toolkit';
	import { OTP_CONFIG } from '$lib/constants/otp';
	import { verifying, confirm, back, resend_code, resend_timer } from '$lib/paraglide/messages';
	import Altcha from './Altcha.svelte';

	const dispatch = createEventDispatcher<{
		submit: { otp: string; altchaPayload?: string };
		back: void;
		resend: void;
		error: { message: string };
	}>();

	export let isSubmitting = false;
	export let errorMessage = '';
	export let resendTimer = 0;
	export let otpInfoMessage = '';
	export let showCaptcha = false;

	let altchaVerified = false;
	let altchaPayload = '';

	let otp = '';

	const debouncedSubmit = debounce(() => {
		if (isSubmitting) return;
		if (otp.length !== OTP_CONFIG.otpLength) return;
		dispatch('submit', { otp, altchaPayload: altchaVerified ? altchaPayload : undefined });
	}, 300);

	function handleSubmit(event: Event) {
		debouncedSubmit();
	}

	function handleBack() {
		setTimeout(() => {
			otp = '';
		}, 0);
		debouncedSubmit.cancel();
		dispatch('back');
	}

	function handleAltchaVerify(event: CustomEvent) {
		altchaVerified = true;
		altchaPayload = event.detail;
		handleSubmit(new Event('submit'));
	}
</script>

<form
	onsubmit={(e) => {
		e.preventDefault();
		handleSubmit(e);
	}}
	class="space-y-4"
>
	<div class="flex flex-col items-center justify-center space-y-4">
		<p class="text-muted-foreground text-center text-sm">
			{otpInfoMessage}
		</p>
		<div class="flex flex-col items-center justify-center space-y-2">
			<InputOTP.Root
				bind:value={otp}
				maxlength={OTP_CONFIG.otpLength}
				pattern={REGEXP_ONLY_DIGITS}
				disabled={isSubmitting}
			>
				{#snippet children({ cells })}
					<InputOTP.Group>
						{#each cells.slice(0, OTP_CONFIG.otpLength) as cell}
							<InputOTP.Slot {cell} class="h-12 w-10 text-lg" />
						{/each}
					</InputOTP.Group>
				{/snippet}
			</InputOTP.Root>
		</div>

		{#if errorMessage}
			<p class="text-sm text-red-500">{errorMessage}</p>
		{/if}

		<div class="flex flex-col space-y-2">
			<Button
				type="submit"
				disabled={isSubmitting || otp.length !== OTP_CONFIG.otpLength}
				data-ph-capture-attribute-action="otp-verification"
				data-ph-capture-attribute-otp-length={otp.length}
				data-ph-capture-attribute-captcha-enabled={showCaptcha}
			>
				{isSubmitting ? verifying() : confirm()}
			</Button>
			<button type="button" onclick={handleBack} data-ph-capture-attribute-action="otp-back">
				{back()}
			</button>
		</div>

		{#if resendTimer === 0}
			<Button
				type="button"
				variant="ghost"
				onclick={() => dispatch('resend')}
				disabled={isSubmitting}
				data-ph-capture-attribute-action="otp-resend"
			>
				{resend_code()}
			</Button>
		{:else}
			<p class="text-center text-sm text-gray-500">{resend_timer({ seconds: resendTimer })}</p>
		{/if}

		{#if showCaptcha && !altchaVerified}
			<div class="mt-4 w-full">
				<Altcha
					on:success={handleAltchaVerify}
					on:error={() => {
						altchaVerified = false;
						altchaPayload = '';
					}}
				/>
			</div>
		{/if}
	</div>
</form>
