<script lang="ts">
	import type { CountryCode, E164Number } from 'svelte-tel-input/types';
	import { Button } from '$lib/components/ui/button';
	import { createEventDispatcher } from 'svelte';
	import { get_code, loading, invalid_phone } from '$lib/paraglide/messages';
	import Altcha from './Altcha.svelte';
	import PhoneInputField from '$lib/components/ui/phone-input/PhoneInputField.svelte';

	const dispatch = createEventDispatcher<{
		submit: { phone: E164Number; altchaPayload?: string };
		error: { message: string };
	}>();

	export let isSubmitting = false;
	export let errorMessage = '';
	export let showCaptcha = false;

	let selectedCountry: CountryCode = 'KZ';
	let phoneValid = false;
	let phone: E164Number | null = '';
	let altchaVerified = false;
	let altchaPayload = '';

	async function handleSubmit(event: Event) {
		if (!phoneValid || !phone) {
			dispatch('error', { message: invalid_phone() });
			return;
		}

		dispatch('submit', { phone, altchaPayload: altchaVerified ? altchaPayload : undefined });
	}

	function handleAltchaVerify(event: CustomEvent) {
		altchaVerified = true;
		altchaPayload = event.detail;
		handleSubmit(new Event('submit'));
	}
</script>

<form
	onsubmit={(e) => {
		e.preventDefault();
		handleSubmit(e);
	}}
	class="space-y-4"
>
	<PhoneInputField
		bind:selectedCountry
		bind:phone
		bind:phoneValid
		bind:errorMessage
		autofocus={true}
		disabled={isSubmitting}
	/>

	{#if showCaptcha && !altchaVerified}
		<div class="mt-4">
			<Altcha
				on:success={handleAltchaVerify}
				on:error={() => {
					altchaVerified = false;
					altchaPayload = '';
				}}
			/>
		</div>
	{/if}

	<Button
		type="submit"
		class="w-full"
		disabled={isSubmitting || !phoneValid}
		data-ph-capture-attribute-auth-method="phone"
		data-ph-capture-attribute-action="login-attempt"
		data-ph-capture-attribute-phone-valid={phoneValid}
		data-ph-capture-attribute-country={selectedCountry}
		data-ph-capture-attribute-captcha-enabled={showCaptcha}
	>
		{isSubmitting ? loading() : get_code()}
	</Button>
</form>
