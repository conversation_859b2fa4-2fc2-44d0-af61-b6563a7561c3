<script lang="ts">
	import 'altcha';
	import { createEventDispatcher, onMount } from 'svelte';
	import { getLocale } from '$lib/paraglide/runtime.js';

	const dispatch = createEventDispatcher();
	export let value: string = '';

	interface AltchaStrings {
		label: string;
		error: string;
		expired: string;
		footer: string;
		verified: string;
		verifying: string;
		waitAlert: string;
	}

	const translations: Record<string, AltchaStrings> = {
		en: {
			label: 'click me',
			error: 'error',
			expired: 'expired',
			footer: 'altcha - spam protection',
			verified: 'verified',
			verifying: 'verifying...',
			waitAlert: 'please wait...'
		},
		ru: {
			label: 'нажми на меня',
			error: 'ошибка',
			expired: 'истекло',
			footer: 'altcha - защита от спама',
			verified: 'подтверждено',
			verifying: 'проверка...',
			waitAlert: 'подождите...'
		}
	};

	onMount(() => {
		let attempts = 0;
		const maxAttempts = 4;
		const interval = 800;

		const checkWidget = setInterval(() => {
			const widget = document.querySelector('altcha-widget') as HTMLElement & {
				configure: (config: { strings: AltchaStrings }) => void;
			};
			attempts++;

			if (widget) {
				clearInterval(checkWidget);
				const currentLocale = getLocale();
				widget.configure({
					strings: translations[currentLocale] || translations.en
				});
			} else if (attempts >= maxAttempts) {
				clearInterval(checkWidget);
				console.error('ALTCHA widget not found after maximum attempts');
			}
		}, interval);

		return () => clearInterval(checkWidget);
	});
</script>

<div class="">
	<altcha-widget
		style="--altcha-max-width:100%;"
		challengeurl="/rest/altcha/challenge"
		onstatechange={(ev: CustomEvent<{ payload: string; state: string }>) => {
			const { payload, state } = ev.detail;
			if (state === 'verified' && payload) {
				value = payload;
				dispatch('success', payload);
			} else {
				value = '';
				dispatch('error');
			}
		}}
	></altcha-widget>
</div>
