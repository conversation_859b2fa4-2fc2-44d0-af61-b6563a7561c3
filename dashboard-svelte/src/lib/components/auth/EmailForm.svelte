<script lang="ts">
	import { Button } from '$lib/components/ui/button';
	import { Input } from '$lib/components/ui/input';
	import { createEventDispatcher } from 'svelte';
	import { get_code, loading, invalid_email } from '$lib/paraglide/messages';
	import Altcha from './Altcha.svelte';

	const dispatch = createEventDispatcher<{
		submit: { email: string; altchaPayload?: string };
		error: { message: string };
	}>();

	export let isSubmitting = false;
	export let errorMessage = '';
	export let showCaptcha = false;

	let email = '';
	let isValidEmail = false;
	let altchaVerified = false;
	let altchaPayload = '';

	$: isValidEmail = validateEmail(email);
	$: if (email) {
		errorMessage = '';
	}

	function validateEmail(email: string): boolean {
		const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
		return emailRegex.test(email);
	}

	async function handleSubmit(event: Event) {
		if (!isValidEmail) {
			dispatch('error', { message: invalid_email() });
			return;
		}

		dispatch('submit', {
			email,
			altchaPayload: showCaptcha && altchaVerified ? altchaPayload : undefined
		});
	}

	function handleAltchaVerify(event: CustomEvent) {
		altchaVerified = true;
		altchaPayload = event.detail;
		handleSubmit(new Event('submit'));
	}
</script>

<form
	onsubmit={(e) => {
		e.preventDefault();
		handleSubmit(e);
	}}
	class="space-y-4"
>
	<div class="space-y-2">
		<Input
			type="email"
			placeholder="<EMAIL>"
			bind:value={email}
			class="w-full"
			autofocus
		/>
		{#if errorMessage}
			<p class="text-destructive mt-2 text-sm">{errorMessage}</p>
		{/if}
	</div>

	{#if showCaptcha && !altchaVerified}
		<div class="mt-4">
			<Altcha
				on:success={handleAltchaVerify}
				on:error={() => {
					altchaVerified = false;
					altchaPayload = '';
				}}
			/>
		</div>
	{/if}

	<Button
		type="submit"
		class="w-full"
		disabled={isSubmitting || !isValidEmail}
		data-ph-capture-attribute-auth-method="email"
		data-ph-capture-attribute-action="login-attempt"
		data-ph-capture-attribute-email-valid={isValidEmail}
		data-ph-capture-attribute-captcha-enabled={showCaptcha}
	>
		{isSubmitting ? loading() : get_code()}
	</Button>
</form>
