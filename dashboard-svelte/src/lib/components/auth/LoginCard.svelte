<script lang="ts">
	import {
		<PERSON>,
		<PERSON><PERSON><PERSON><PERSON>,
		<PERSON><PERSON><PERSON>nt,
		CardTitle,
		CardDescription
	} from '$lib/components/ui/card';
	import * as Tabs from '$lib/components/ui/tabs';
	import { auth } from '$lib/stores/auth';
	import { goto } from '$app/navigation';
	import { page } from '$app/stores';
	import { toast } from 'svelte-sonner';
	import { onMount } from 'svelte';
	import type { E164Number } from 'svelte-tel-input/types';
	import { authService } from '$lib/services/auth';
	import { StatusEnum } from '$lib/types/auth.types';
	import type { VerifyResponse } from '$lib/types/auth.types';
	import { OTP_CONFIG } from '$lib/constants/otp';
	import { preferencesStore } from '$lib/stores/preferences-store';
	import { analytics } from '$lib/services/analytics';
	import {
		welcome_to_onai,
		making_business_easier,
		whatsapp_number,
		email_address,
		login_method,
		confirmation_code,
		code_sent_to_whatsapp,
		code_sent_to_email,
		failed_to_send_code,
		successful_authorization,
		failed_to_verify_code,
		otp_whatsapp_info,
		otp_email_info
	} from '$lib/paraglide/messages';

	import PhoneForm from './PhoneForm.svelte';
	import EmailForm from './EmailForm.svelte';
	import OTPForm from './OTPForm.svelte';
	import { logger } from '$lib/client/telemetry';
	import { SeverityNumber } from '@opentelemetry/api-logs';

	let activeTab = 'phone';
	let isSubmitting = false;
	let errorMessage = '';
	let phone: E164Number | null = null;
	let email: string | null = null;
	let resendTimer = 0;
	let showPhoneCaptcha = false;
	let showEmailCaptcha = false;
	let showOtpCaptcha = false;
	let otpInfoMessage = '';
	let identifier = '';

	function startResendTimer() {
		resendTimer = OTP_CONFIG.resendTimer;
		const timer = setInterval(() => {
			resendTimer--;
			if (resendTimer <= 0) {
				clearInterval(timer);
			}
		}, 1000);
	}

	interface LoginResponseWithCaptcha {
		status: StatusEnum;
		message: string;
		captcha_required: boolean;
	}

	interface ExtendedLoginRequest {
		email?: string;
		phone?: string;
		captcha_solution?: any;
	}

	type LoginMode = 'phone' | 'email';

	async function handleLogin(mode: LoginMode, identifierValue: string, altchaPayload?: string) {
		if (isSubmitting) return;

		// Track login attempt
		analytics.trackLoginAttempt(
			mode,
			mode === 'phone' ? identifierValue : undefined,
			mode === 'email' ? identifierValue : undefined
		);

		phone = mode === 'phone' ? (identifierValue as E164Number) : null;
		email = mode === 'email' ? identifierValue : null;

		isSubmitting = true;
		errorMessage = '';

		const request: ExtendedLoginRequest = {
			captcha_solution: altchaPayload
		};
		if (mode === 'phone') request.phone = identifierValue;
		else request.email = identifierValue;

		try {
			const response = (await authService.login(request)) as LoginResponseWithCaptcha;

			if (response.status === StatusEnum.SUCCESS) {
				const captchaRequired = response.captcha_required && !altchaPayload;

				if (captchaRequired) {
					if (mode === 'phone') {
						showPhoneCaptcha = true;
						activeTab = 'phone';
					} else {
						showEmailCaptcha = true;
						activeTab = 'email';
					}
					isSubmitting = false;
					return;
				}

				// Track OTP request success
				analytics.trackOtpRequested(mode, identifierValue);

				identifier = identifierValue;
				activeTab = 'otp';
				otpInfoMessage = mode === 'phone' ? otp_whatsapp_info() : otp_email_info();
				startResendTimer();

				showPhoneCaptcha = false;
				showEmailCaptcha = false;

				toast.success(mode === 'phone' ? code_sent_to_whatsapp() : code_sent_to_email());
			} else {
				errorMessage = response.message || failed_to_send_code();
				toast.error(errorMessage);
			}
		} catch (error) {
			errorMessage = error instanceof Error ? error.message : failed_to_send_code();
			toast.error(errorMessage);
		} finally {
			isSubmitting = false;
		}
	}

	const handlePhoneSubmit = ({
		detail
	}: CustomEvent<{ phone: E164Number; altchaPayload?: string }>) =>
		handleLogin('phone', detail.phone, detail.altchaPayload);

	const handleEmailSubmit = ({ detail }: CustomEvent<{ email: string; altchaPayload?: string }>) =>
		handleLogin('email', detail.email, detail.altchaPayload);

	function handleBack() {
		activeTab = email ? 'email' : 'phone';
		errorMessage = '';
	}

	function handleResend() {
		if (email) {
			handleEmailSubmit(new CustomEvent('submit', { detail: { email } }));
		} else if (phone) {
			handlePhoneSubmit(new CustomEvent('submit', { detail: { phone } }));
		}
	}

	async function handleOtpSubmit({ detail }: CustomEvent<{ otp: string; altchaPayload?: string }>) {
		if (isSubmitting) return;

		if (!identifier) return;

		isSubmitting = true;
		errorMessage = '';

		const method = email ? 'email' : 'phone';

		try {
			const response = (await authService.verifyOtp({
				identifier: identifier,
				otp_code: detail.otp,
				captcha_solution: detail.altchaPayload
			})) as VerifyResponse & { captcha_required?: boolean };

			if (response.status === StatusEnum.SUCCESS) {
				if (response.captcha_required && !detail.altchaPayload) {
					showOtpCaptcha = true;
					isSubmitting = false;
					return;
				}

				// Track successful OTP verification and login
				analytics.trackOtpVerified(method, true);
				analytics.trackLoginSuccess(method, response.account.id);

				auth.login(response.account);

				toast.success(successful_authorization());

				const returnTo = $page.url.searchParams.get('returnTo');
				if (returnTo && returnTo.startsWith('/')) {
					logger.emit({
						body: `returnTo ${returnTo}`,
						severityNumber: SeverityNumber.INFO,
						severityText: 'INFO'
					});
					window.location.href = returnTo;
				} else {
					logger.emit({
						body: 'redirecting to dashboard',
						severityNumber: SeverityNumber.INFO,
						severityText: 'INFO'
					});
					window.location.href = '/dashboard';
				}
			} else {
				// Track failed OTP verification
				analytics.trackOtpVerified(method, false);

				errorMessage = response.message || failed_to_verify_code();
				toast.error(errorMessage);
			}
		} catch (error) {
			// Track failed OTP verification
			analytics.trackOtpVerified(method, false);

			errorMessage = error instanceof Error ? error.message : failed_to_verify_code();
			toast.error(errorMessage);
		} finally {
			isSubmitting = false;
		}
	}
</script>

<div class="flex items-center justify-center px-4 py-12 sm:px-6 lg:px-8">
	<Card class="w-full max-w-md">
		<CardHeader>
			<CardTitle class="text-center">{welcome_to_onai()}</CardTitle>
			<CardDescription class="text-center">{making_business_easier()}</CardDescription>
		</CardHeader>
		<CardContent class="pt-1">
			<Tabs.Root bind:value={activeTab}>
				<Tabs.List class="mb-4 grid grid-cols-2">
					<Tabs.Trigger
						value="phone"
						class="rounded-tl rounded-tr"
						data-ph-capture-attribute-auth-method="phone"
						data-ph-capture-attribute-action="method-selection"
					>
						{whatsapp_number()}
					</Tabs.Trigger>
					<Tabs.Trigger
						value="email"
						class="rounded-tl rounded-tr"
						data-ph-capture-attribute-auth-method="email"
						data-ph-capture-attribute-action="method-selection"
					>
						{email_address()}
					</Tabs.Trigger>
				</Tabs.List>

				<Tabs.Content value="phone">
					<PhoneForm
						{isSubmitting}
						{errorMessage}
						showCaptcha={showPhoneCaptcha}
						on:submit={handlePhoneSubmit}
						on:error={({ detail }) => {
							errorMessage = detail.message;
							toast.error(detail.message);
						}}
					/>
				</Tabs.Content>

				<Tabs.Content value="email">
					<EmailForm
						{isSubmitting}
						{errorMessage}
						showCaptcha={showEmailCaptcha}
						on:submit={handleEmailSubmit}
						on:error={({ detail }) => {
							errorMessage = detail.message;
							toast.error(detail.message);
						}}
					/>
				</Tabs.Content>

				<Tabs.Content value="otp">
					<OTPForm
						{isSubmitting}
						{errorMessage}
						{resendTimer}
						{otpInfoMessage}
						showCaptcha={showOtpCaptcha}
						on:submit={handleOtpSubmit}
						on:back={handleBack}
						on:resend={handleResend}
						on:error={({ detail }) => {
							errorMessage = detail.message;
							toast.error(detail.message);
						}}
					/>
				</Tabs.Content>
			</Tabs.Root>
		</CardContent>
	</Card>
</div>
