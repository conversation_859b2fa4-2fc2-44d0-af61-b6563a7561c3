<script lang="ts">
	import {
		users,
		loading as loadingMsg,
		select_channel,
		no_users_available,
		no_funnels_available,
		users_search,
		users_status_enabled,
		users_status_disabled,
		users_enable_button,
		users_disable_button,
		users_pings,
		users_reset_button,
		users_reset_all_button,
		users_reset_all_button_short,
		users_reset_button_short,
		users_enable_button_short,
		users_disable_button_short,
		analytics_all_channels
	} from '$lib/paraglide/messages';
	import { onMount } from 'svelte';
	import { Input } from '$lib/components/ui/input';
	import { Button } from '$lib/components/ui/button';
	import * as Select from '$lib/components/ui/select';
	import { channelStore } from '$lib/stores/channel-store';
	import { usersStore } from '$lib/stores/users-store';
	import type { ChatUser } from '$lib/services/user';
	import type { ChannelResponse } from '$lib/types/channel.types';
	import { debounce } from 'es-toolkit';
	import IconEnable from '~icons/lucide/check-circle';
	import IconDisable from '~icons/lucide/x-circle';
	import IconReset from '~icons/lucide/refresh-cw';
	import IconWhatsApp from '~icons/logos/whatsapp-icon';
	import IconTelegram from '~icons/logos/telegram';
	import IconChannel from '~icons/lucide/hash';
	import ResetMessagesDialog from './ResetMessagesDialog.svelte';
	import { ResetAction } from '$lib/constants/reset-actions';

	export let channels: ChannelResponse[] = [];
	export let onAiId: string;

	$: ({
		selectedChannelId,
		query,
		results,
		loading,
		error,
		processingUsers,
		showResetDialog,
		resetAction
	} = $usersStore);

	function getChannelLabel(id: string | undefined): string {
		if (id === 'all') return analytics_all_channels();
		const ch = channels.find((c) => c.id === id);
		return (ch?.name ?? ch?.transporter_type ?? ch?.id ?? '').toLowerCase();
	}

	function getUserChannelInfo(user: ChatUser) {
		const channel = channels.find((c) => c.id === user.channel_id);
		const type = channel?.transporter_type ?? 'unknown';
		const name = channel?.name ?? channel?.transporter_type ?? user.channel_id;

		return {
			name,
			type,
			icon: getChannelIcon(type),
			displayName: name.toLowerCase()
		};
	}

	function getChannelIcon(type: string) {
		switch (type.toLowerCase()) {
			case 'whatsapp':
			case 'waba':
				return IconWhatsApp;
			case 'telegram':
				return IconTelegram;
			default:
				return IconChannel;
		}
	}

	onMount(() => {
		if (!channels?.length && onAiId) {
			channelStore.fetchChannels(onAiId);
		}

		const unsubscribe = channelStore.subscribe((state) => {
			if (!channels.length && state.channels.length) {
				channels = state.channels;
			}
		});

		usersStore.initialize();
		searchUsers();

		return unsubscribe;
	});

	function searchUsers() {
		usersStore.searchUsers(onAiId);
	}

	const debouncedSearch = debounce(searchUsers, 300);

	$: (query, selectedChannelId, debouncedSearch());
</script>

<div class="space-y-4">
	<h2 class="text-xl font-semibold">{users()}</h2>

	<div class="flex flex-col items-start gap-2 sm:flex-row">
		<div class="mb-2 w-full sm:mb-0 sm:w-auto sm:min-w-[180px]">
			<Select.Root
				disabled={channels.length === 0}
				type="single"
				value={selectedChannelId}
				onValueChange={(value) => usersStore.setSelectedChannelId(value)}
			>
				<Select.Trigger class="w-full"
					>{selectedChannelId
						? getChannelLabel(selectedChannelId)
						: select_channel()}</Select.Trigger
				>
				<Select.Content>
					<Select.Item value="all">{analytics_all_channels()}</Select.Item>
					{#each channels as ch}
						<Select.Item value={ch.id}
							>{(ch.name ?? ch.transporter_type ?? ch.id ?? '').toLowerCase()}</Select.Item
						>
					{/each}
				</Select.Content>
			</Select.Root>
		</div>

		<div class="flex w-full gap-2">
			<Input
				disabled={channels.length === 0}
				class="flex-1"
				placeholder={users_search()}
				value={query}
				oninput={(e) => usersStore.setQuery((e.target as HTMLInputElement)?.value || '')}
			/>

			<Button
				disabled={channels.length === 0}
				onclick={searchUsers}
				variant="default"
				class="h-10 whitespace-nowrap px-4"
			>
				{users_search()}
			</Button>
		</div>
	</div>

	{#if selectedChannelId && selectedChannelId !== 'all'}
		<div class="flex flex-wrap gap-2">
			<Button
				size="sm"
				variant="outline"
				onclick={() => usersStore.openResetDialog(ResetAction.ResetAll)}
				class="flex items-center gap-1"
			>
				<IconReset class="h-4 w-4" />
				<span class="hidden sm:inline">{users_reset_all_button()}</span>
				<span class="sm:hidden">{users_reset_all_button_short()}</span>
			</Button>
		</div>
	{/if}

	{#if loading}
		<p>{loadingMsg()}</p>
	{:else if channels.length === 0}
		<p class="text-muted-foreground">{no_funnels_available()}</p>
	{:else if error}
		<p class="text-destructive">{error}</p>
	{:else if results.length === 0}
		<p class="text-muted-foreground">{no_users_available()}</p>
	{:else}
		<ul class="space-y-1">
			{#each results as user}
				<li
					class="flex flex-col justify-between gap-2 rounded border p-2 sm:flex-row sm:items-center"
				>
					<div class="flex flex-col">
						<span class="break-all">{user.user_id}</span>
						<div class="text-muted-foreground flex items-center gap-2 text-xs">
							<span class={user.disabled ? 'text-red-600' : 'text-green-600'}
								>{user.disabled ? users_status_disabled() : users_status_enabled()}</span
							>
							{#if selectedChannelId === 'all'}
								{@const channelInfo = getUserChannelInfo(user)}
								<span class="text-gray-400">•</span>
								<div class="flex items-center gap-1">
									<svelte:component this={channelInfo.icon} class="h-3 w-3" />
									<span class="font-medium text-blue-600">{channelInfo.displayName}</span>
								</div>
							{:else if channels.length > 1}
								{@const channelInfo = getUserChannelInfo(user)}
								<span class="text-gray-400">•</span>
								<div class="flex items-center gap-1">
									<svelte:component this={channelInfo.icon} class="h-3 w-3 opacity-60" />
									<span class="text-xs text-gray-500">{channelInfo.displayName}</span>
								</div>
							{/if}
						</div>
					</div>
					<div class="flex flex-wrap items-center gap-2">
						<div class="flex flex-wrap items-center gap-1">
							<Button
								size="sm"
								variant="ghost"
								onclick={() => usersStore.openResetDialog(ResetAction.Reset, user.user_id)}
								disabled={processingUsers.has(user.user_id)}
								class="flex items-center gap-1 text-blue-600 hover:text-blue-700"
							>
								<IconReset class="h-4 w-4" />
								<span class="hidden sm:inline">{users_reset_button()}</span>
								<span class="sm:hidden">{users_reset_button_short()}</span>
							</Button>
						</div>
						{#if user.disabled}
							<Button
								size="sm"
								variant="ghost"
								onclick={() => usersStore.enableUser(user)}
								disabled={processingUsers.has(user.user_id)}
								class="flex items-center gap-1 text-green-600 hover:text-green-700"
							>
								<IconEnable class="h-4 w-4" />
								<span class="hidden sm:inline">{users_enable_button()}</span>
								<span class="sm:hidden">{users_enable_button_short()}</span>
							</Button>
						{:else}
							<Button
								size="sm"
								variant="ghost"
								onclick={() => usersStore.disableUser(user)}
								disabled={processingUsers.has(user.user_id)}
								class="flex items-center gap-1 text-red-600 hover:text-red-700"
							>
								<IconDisable class="h-4 w-4" />
								<span class="hidden sm:inline">{users_disable_button()}</span>
								<span class="sm:hidden">{users_disable_button_short()}</span>
							</Button>
						{/if}
					</div>
				</li>
			{/each}
		</ul>
	{/if}
</div>

{#if showResetDialog}
	<ResetMessagesDialog
		open={showResetDialog}
		action={resetAction.type}
		userId={resetAction.userId}
		onConfirm={() => usersStore.handleResetConfirm()}
	/>
{/if}
