<script lang="ts">
	import { knowledgeStore } from '$lib/stores/knowledge-store';
	import { knowledgeFormStore } from '$lib/stores/knowledge-form-store';
	import { knowledgeFormUiStore } from '$lib/stores/knowledge-form-ui-store';
	import { type KnowledgeType } from '$lib/types/knowledge.types';
	import { onAiStore } from '$lib/stores/onai-store';
	import { analytics } from '$lib/services/analytics';
	import { toast } from 'svelte-sonner';
	import { onMount } from 'svelte';
	import { Skeleton } from '$lib/components/ui/skeleton';
	import HistoryDialog from './HistoryDialog.svelte';
	import KnowledgeHistoryForm from './KnowledgeHistoryForm.svelte';
	import KnowledgeDeleteDialog from './KnowledgeDeleteDialog.svelte';
	import { getContentTypeMapping } from '$lib/constants/files';
	import KnowledgeTypeSelector from './KnowledgeTypeSelector.svelte';
	import FileUploadProgress from './FileUploadProgress.svelte';
	import TextKnowledgeForm from './TextKnowledgeForm.svelte';
	import DocumentKnowledgeForm from './DocumentKnowledgeForm.svelte';
	import KnowledgeFormActions from './KnowledgeFormActions.svelte';
	import {
		knowledge_inline_content_error,
		knowledge_file_upload_error,
		knowledge_file_upload_success,
		knowledge_file_update_success,
		knowledge_content_update_success,
		knowledge_content_save_success,
		knowledge_download_error,
		knowledge_version_restored,
		file_upload_files_selected,
		file_upload_uploading,
		file_upload_upload_success,
		file_upload_upload_partial,
		file_upload_upload_failed,
		file_upload_failed,
		knowledge_save_text_error,
		knowledge_save_document_error,
		knowledge_delete_file_error,
		history_title,
		history_description
	} from '$lib/paraglide/messages';

	export let type: KnowledgeType;
	export let initialName: string = '';
	export let onClose: () => void;
	export let open: boolean = true;

	let hasInitialized = false;

	// Initialize stores only once when opened
	$: if (open && !hasInitialized) {
		knowledgeFormStore.initialize(type, initialName);
		knowledgeFormUiStore.reset();
		loadAllKnowledge();
		hasInitialized = true;
	}

	// Reset when closed
	$: if (!open) {
		hasInitialized = false;
	}

	// Reactive variables derived from stores
	$: formState = $knowledgeFormStore;
	$: uiState = $knowledgeFormUiStore;
	$: textState = formState.text;
	$: documentState = formState.document;

	// Computed properties
	$: canDownloadText = textState.content !== '';
	$: canDeleteText = textState.knowledgeExists && !!textState.knowledgeId;
	$: currentKnowledgeId =
		formState.type === 'text' ? textState.knowledgeId : documentState.knowledgeId;
	$: hasFileErrors = knowledgeFormUiStore.getFileErrors(uiState);
	$: hasUploadingFiles = knowledgeFormUiStore.getUploadingFiles(uiState);
	$: canSave = !(
		(formState.type === 'document' && documentState.isSaving) ||
		(formState.type === 'text' && textState.isSaving) ||
		(formState.type === 'document' &&
			documentState.selectedFiles.length === 0 &&
			!documentState.selectedFile &&
			!documentState.content) ||
		(formState.type === 'text' && !textState.inlineContent) ||
		uiState.isUploading ||
		hasUploadingFiles
	);
	$: showHistory =
		(formState.type === 'text' && textState.knowledgeExists && !!textState.knowledgeId) ||
		(formState.type === 'document' && documentState.knowledgeExists && !!documentState.knowledgeId);
	$: isSaving = formState.type === 'text' ? textState.isSaving : documentState.isSaving;
	$: knowledgeExists =
		formState.type === 'text' ? textState.knowledgeExists : documentState.knowledgeExists;

	onMount(() => {
		if (!hasInitialized) {
			knowledgeFormStore.initialize(type, initialName);
			loadAllKnowledge();
			hasInitialized = true;
		}
	});

	function createDownloadLink(
		content: string,
		filename: string,
		contentType: string,
		fileExtension: string
	): void {
		const blob = new Blob([content], { type: contentType });
		const url = URL.createObjectURL(blob);
		const a = document.createElement('a');
		a.href = url;
		a.download = filename + fileExtension;
		document.body.appendChild(a);
		a.click();
		document.body.removeChild(a);
		URL.revokeObjectURL(url);
	}

	function readFileAsText(file: File): Promise<string> {
		return new Promise((resolve, reject) => {
			const reader = new FileReader();
			reader.onload = () => {
				if (typeof reader.result === 'string') {
					resolve(reader.result);
				} else {
					reject(new Error('Failed to read file as text'));
				}
			};
			reader.onerror = () => reject(reader.error);
			reader.readAsText(file);
		});
	}

	async function loadAllKnowledge() {
		knowledgeFormUiStore.setLoading(true);

		// Load text knowledge
		knowledgeFormStore.setTextLoading(true);
		try {
			await knowledgeStore.loadKnowledgeByType('text');
			const onAiId = $onAiStore.api?.id;
			if (onAiId) {
				const textKnowledgeItem = Object.values($knowledgeStore.items).find(
					(item) => item.type === 'text' && item.name && item.content
				);
				if (textKnowledgeItem && textKnowledgeItem.content) {
					knowledgeFormStore.loadTextKnowledge(
						textKnowledgeItem.content,
						textKnowledgeItem.name,
						textKnowledgeItem.id!
					);
				} else {
					knowledgeFormStore.resetTextKnowledge();
				}
			}
		} catch (error) {
			console.error('Error loading text knowledge:', error);
			knowledgeFormStore.resetTextKnowledge();
		}
		knowledgeFormStore.setTextLoading(false);

		// Load document knowledge
		knowledgeFormStore.setDocumentLoading(true);
		try {
			await knowledgeStore.loadKnowledgeByType('document');
			const onAiId = $onAiStore.api?.id;
			if (onAiId) {
				const documentKnowledgeItems = Object.values($knowledgeStore.items).filter(
					(item) => item.type === 'document' && item.name && item.content
				);
				if (documentKnowledgeItems.length > 0) {
					// For backward compatibility, use the first item as primary
					const primaryItem = documentKnowledgeItems[0];
					const knowledgeIds = documentKnowledgeItems
						.map((item) => item.id)
						.filter((id): id is string => id !== null);
					const existingNames = documentKnowledgeItems
						.map((item) => item.name)
						.filter((name): name is string => name !== null);

					knowledgeFormStore.loadDocumentKnowledge(
						primaryItem.content,
						primaryItem.name,
						primaryItem.id!,
						knowledgeIds,
						existingNames
					);
				} else {
					knowledgeFormStore.resetDocumentKnowledge();
				}
			}
		} catch (error) {
			console.error('Error loading document knowledge:', error);
			knowledgeFormStore.resetDocumentKnowledge();
		}
		knowledgeFormStore.setDocumentLoading(false);

		knowledgeFormUiStore.setLoading(false);
	}

	async function handleSaveText() {
		if (!textState.inlineContent) {
			toast.error(knowledge_inline_content_error());
			return;
		}

		knowledgeFormStore.setTextSaving(true);
		knowledgeFormUiStore.setUploading(true, 'saving text content...');
		knowledgeFormUiStore.setUploadProgress(0);

		// Simulate progress
		const progressInterval = setInterval(() => {
			if (uiState.uploadProgress < 90) {
				knowledgeFormUiStore.setUploadProgress(uiState.uploadProgress + 10);
			}
		}, 100);

		try {
			if (textState.knowledgeExists && textState.knowledgeId) {
				await knowledgeStore.updateKnowledge(
					'text',
					textState.knowledgeId,
					textState.inlineContent,
					textState.name
				);

				// Track knowledge update
				analytics.trackKnowledgeUpdated('text', textState.name || 'inline-content', onAi?.id || '');

				toast.success(knowledge_content_update_success());
			} else {
				await knowledgeStore.createKnowledge(
					'text',
					textState.name || 'inline-content',
					textState.inlineContent
				);

				// Track knowledge creation
				analytics.trackKnowledgeCreated('text', textState.name || 'inline-content', onAi?.id || '');

				toast.success(knowledge_content_save_success());
			}
			knowledgeFormUiStore.setUploadProgress(100);
		} catch (error) {
			console.error('Error saving text:', error);
			toast.error(knowledge_save_text_error());
		} finally {
			clearInterval(progressInterval);
			knowledgeFormUiStore.setUploadProgress(100);
			setTimeout(() => {
				knowledgeFormStore.setTextSaving(false);
				knowledgeFormUiStore.setUploading(false);
			}, 500);
		}
	}

	async function handleSaveDocument() {
		if (documentState.selectedFiles.length === 0 && !documentState.content) {
			toast.error(knowledge_file_upload_error());
			return;
		}

		knowledgeFormStore.setDocumentSaving(true);
		knowledgeFormUiStore.setUploading(true);
		knowledgeFormUiStore.setUploadProgress(0);

		try {
			if (documentState.selectedFiles.length > 0) {
				// Handle multiple files with individual progress tracking
				const plural = documentState.selectedFiles.length !== 1 ? 's' : '';
				knowledgeFormUiStore.setUploadStatusText(
					file_upload_files_selected({ count: documentState.selectedFiles.length, plural })
				);
				const totalFiles = documentState.selectedFiles.length;
				let completedFiles = 0;
				let successCount = 0;
				let errorCount = 0;

				for (let i = 0; i < documentState.selectedFiles.length; i++) {
					const file = documentState.selectedFiles[i];
					knowledgeFormUiStore.setUploadStatusText(
						file_upload_uploading({
							fileName: file.name,
							current: i + 1,
							total: totalFiles
						})
					);

					// Update individual file status
					knowledgeFormUiStore.setFileUploadStatus(file.name, {
						progress: 0,
						status: 'uploading'
					});

					try {
						// Simulate progress for file reading
						knowledgeFormUiStore.setFileUploadStatus(file.name, {
							progress: 30,
							status: 'uploading'
						});

						const fileContent = await readFileAsText(file);

						// Simulate progress for upload
						knowledgeFormUiStore.setFileUploadStatus(file.name, {
							progress: 70,
							status: 'uploading'
						});

						const knowledgeId = await knowledgeStore.createKnowledge(
							'document',
							file.name,
							fileContent
						);

						// Track file upload success
						analytics.trackFileUpload(file.name, file.size, file.type, true);

						// Track knowledge creation
						analytics.trackKnowledgeCreated('document', file.name, onAi?.id || '');

						// Mark as completed
						knowledgeFormUiStore.setFileUploadStatus(file.name, {
							progress: 100,
							status: 'success',
							knowledgeId: knowledgeId || undefined
						});

						completedFiles++;
						successCount++;
						knowledgeFormUiStore.setUploadProgress(Math.round((completedFiles / totalFiles) * 100));
					} catch (error) {
						// Track file upload failure
						analytics.trackFileUpload(file.name, file.size, file.type, false);

						knowledgeFormUiStore.setFileUploadStatus(file.name, {
							progress: 0,
							status: 'error',
							error: error instanceof Error ? error.message : file_upload_failed()
						});
						errorCount++;
					}
				}

				if (errorCount === 0) {
					const plural = successCount !== 1 ? 's' : '';
					toast.success(file_upload_upload_success({ count: successCount, plural }));
				} else if (successCount === 0) {
					const plural = errorCount !== 1 ? 's' : '';
					toast.error(file_upload_upload_failed({ count: errorCount, plural }));
				} else {
					const successPlural = successCount !== 1 ? 's' : '';
					toast.success(
						file_upload_upload_partial({
							success: successCount,
							successPlural,
							failed: errorCount
						})
					);
				}
			} else if (documentState.selectedFile) {
				// Handle single file (backward compatibility)
				knowledgeFormUiStore.setUploadStatusText(`uploading ${documentState.selectedFile.name}...`);
				knowledgeFormUiStore.setUploadProgress(20);

				const fileContent = await readFileAsText(documentState.selectedFile);
				knowledgeFormUiStore.setUploadProgress(60);

				if (documentState.knowledgeExists && documentState.knowledgeId) {
					await knowledgeStore.updateKnowledge(
						'document',
						documentState.knowledgeId,
						fileContent,
						documentState.name
					);

					// Track knowledge update
					analytics.trackKnowledgeUpdated('document', documentState.name, onAi?.id || '');

					toast.success(knowledge_file_update_success());
				} else {
					await knowledgeStore.createKnowledge('document', documentState.name, fileContent);

					// Track knowledge creation
					analytics.trackKnowledgeCreated('document', documentState.name, onAi?.id || '');

					toast.success(knowledge_file_upload_success());
				}
				knowledgeFormUiStore.setUploadProgress(100);
			}
		} catch (error) {
			console.error('Error saving document:', error);
			toast.error(knowledge_save_document_error());
		} finally {
			setTimeout(() => {
				knowledgeFormStore.setDocumentSaving(false);
				knowledgeFormUiStore.setUploading(false);
			}, 500);
		}
	}

	async function handleSave() {
		if (formState.type === 'document') {
			await handleSaveDocument();
		} else {
			await handleSaveText();
		}
		onClose();
	}

	function handleFileSelected(event: CustomEvent<{ file: File }>) {
		knowledgeFormStore.setSelectedFile(event.detail.file);
		knowledgeFormStore.setDocumentName(event.detail.file.name);
	}

	function handleFilesSelected(event: CustomEvent<{ files: File[] }>) {
		knowledgeFormStore.setSelectedFiles(event.detail.files);

		// Initialize status for each file
		knowledgeFormUiStore.clearFileUploadStatus();
		event.detail.files.forEach((file) => {
			knowledgeFormUiStore.setFileUploadStatus(file.name, {
				progress: 0,
				status: 'pending'
			});
		});

		if (event.detail.files.length === 1) {
			knowledgeFormStore.setDocumentName(event.detail.files[0].name);
		} else if (event.detail.files.length > 1) {
			const plural = event.detail.files.length !== 1 ? 's' : '';
			knowledgeFormStore.setDocumentName(
				file_upload_files_selected({ count: event.detail.files.length, plural })
			);
		}
	}

	function handleClearAllFiles() {
		knowledgeFormStore.clearSelectedFiles();
		knowledgeFormUiStore.clearFileUploadStatus();
		knowledgeFormStore.setDocumentName(initialName);
	}

	function removeFile(fileToRemove: File) {
		knowledgeFormStore.removeSelectedFile(fileToRemove);
		knowledgeFormUiStore.removeFileUploadStatus(fileToRemove.name);

		// Update document name
		if (documentState.selectedFiles.length === 0) {
			knowledgeFormStore.setDocumentName(initialName);
		} else if (documentState.selectedFiles.length === 1) {
			knowledgeFormStore.setDocumentName(documentState.selectedFiles[0].name);
		} else {
			const plural = documentState.selectedFiles.length !== 1 ? 's' : '';
			knowledgeFormStore.setDocumentName(
				file_upload_files_selected({ count: documentState.selectedFiles.length, plural })
			);
		}
	}

	async function downloadFile(fileName?: string, knowledgeId?: string) {
		try {
			knowledgeFormUiStore.setLoading(true);

			// If specific parameters provided, use them (for individual file download)
			const targetKnowledgeId =
				knowledgeId ||
				(formState.type === 'text' ? textState.knowledgeId : documentState.knowledgeId);
			const targetType = formState.type;

			if (!targetKnowledgeId) {
				throw new Error('No knowledge ID available');
			}

			const fileData = await knowledgeStore.fetchKnowledgeContentForDownload(
				targetType,
				targetKnowledgeId
			);
			if (!fileData) {
				throw new Error('Failed to fetch file from backend');
			}

			const filenameWithExt =
				fileName || fileData.name || (targetType === 'text' ? 'text.txt' : 'document');
			const { contentType, extension: fileExtension } = getContentTypeMapping(filenameWithExt);

			const lastDotIndex = filenameWithExt.lastIndexOf('.');
			const baseFilename =
				lastDotIndex !== -1 ? filenameWithExt.substring(0, lastDotIndex) : filenameWithExt;

			createDownloadLink(fileData.content, baseFilename, contentType, fileExtension);
		} catch (error) {
			console.error('Error downloading file:', error);
			toast.error(knowledge_download_error());
		} finally {
			knowledgeFormUiStore.setLoading(false);
		}
	}

	// Wrapper functions for onclick handlers
	async function handleDownloadDefault() {
		await downloadFile();
	}

	async function handleDownloadFile(fileName: string, knowledgeId: string) {
		await downloadFile(fileName, knowledgeId);
	}

	async function deleteKnowledgeFile(knowledgeId: string, fileName: string) {
		try {
			knowledgeFormStore.removeDocumentFromIds(knowledgeId);
			// Set current knowledge ID for delete dialog
			knowledgeFormUiStore.setShowDeleteDialog(true);
		} catch (error) {
			console.error('Error deleting file:', error);
			toast.error(knowledge_delete_file_error());
		}
	}

	async function handleRestoreText(restoredContent: string) {
		knowledgeFormStore.setTextContent(restoredContent);
		knowledgeFormStore.setTextInlineContent(restoredContent);
		toast.success(knowledge_version_restored());
	}

	async function handleRestoreDocument(restoredContent: string) {
		knowledgeFormStore.setDocumentContent(restoredContent);
		toast.success(knowledge_version_restored());
	}

	function handleHistory() {
		if (formState.type === 'text') {
			knowledgeFormUiStore.setShowTextHistory(true);
		} else {
			knowledgeFormUiStore.setShowDocumentHistory(true);
		}
	}

	function handleDeleteText() {
		knowledgeFormUiStore.setShowDeleteDialog(true);
	}

	function handleTypeChange(newType: KnowledgeType) {
		knowledgeFormStore.setType(newType);
	}
</script>

<div class="flex h-full flex-col">
	{#if uiState.isLoading}
		<div class="py-8">
			<Skeleton class="h-[100px] w-full" />
		</div>
	{:else if uiState.isUploading}
		<FileUploadProgress
			uploadProgress={uiState.uploadProgress}
			uploadStatusText={uiState.uploadStatusText}
		/>
	{:else}
		<div class="flex flex-1 flex-col gap-3 overflow-hidden">
			<KnowledgeTypeSelector value={formState.type} onChange={handleTypeChange} />

			{#if formState.type === 'document'}
				<div class="flex-1 overflow-hidden">
					<DocumentKnowledgeForm
						bind:selectedFile={documentState.selectedFile}
						bind:selectedFiles={documentState.selectedFiles}
						bind:fileError={documentState.fileError}
						fileUploadStatus={uiState.fileUploadStatus}
						existingDocumentNames={documentState.existingDocumentNames}
						knowledgeIds={documentState.knowledgeIds}
						onFileSelected={handleFileSelected}
						onFilesSelected={handleFilesSelected}
						onClearAllFiles={handleClearAllFiles}
						onRemoveFile={removeFile}
						onDownloadFile={handleDownloadFile}
						onDeleteFile={deleteKnowledgeFile}
					/>
				</div>
			{:else}
				<TextKnowledgeForm
					bind:textInlineContent={textState.inlineContent}
					{canDownloadText}
					{canDeleteText}
					onDownload={handleDownloadDefault}
					onDelete={handleDeleteText}
					maxLength={40000}
					promptTokens={textState.tokenCount}
					isTokenLoading={textState.areTokensLoading}
				/>
			{/if}
		</div>
	{/if}

	<KnowledgeFormActions
		type={formState.type}
		{showHistory}
		{canSave}
		{isSaving}
		{knowledgeExists}
		onHistory={handleHistory}
		onCancel={onClose}
		onSave={handleSave}
	/>
</div>

{#if uiState.showTextHistory && textState.knowledgeId}
	<HistoryDialog
		open={uiState.showTextHistory}
		title={history_title()}
		description={history_description()}
	>
		<KnowledgeHistoryForm
			type="text"
			knowledgeId={textState.knowledgeId!}
			onRestore={handleRestoreText}
			open={!!uiState.showTextHistory}
			onClose={() => knowledgeFormUiStore.setShowTextHistory(false)}
		/>
	</HistoryDialog>
{/if}

{#if uiState.showDocumentHistory && documentState.knowledgeId}
	<HistoryDialog
		open={uiState.showDocumentHistory}
		title={history_title()}
		description={history_description()}
	>
		<KnowledgeHistoryForm
			type="document"
			knowledgeId={documentState.knowledgeId!}
			onRestore={handleRestoreDocument}
			open={!!uiState.showDocumentHistory}
			onClose={() => knowledgeFormUiStore.setShowDocumentHistory(false)}
		/>
	</HistoryDialog>
{/if}

<KnowledgeDeleteDialog
	bind:open={uiState.showDeleteDialog}
	knowledgeId={currentKnowledgeId}
	type={formState.type}
	onDeleted={() => loadAllKnowledge()}
/>
