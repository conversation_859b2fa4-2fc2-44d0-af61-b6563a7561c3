<script lang="ts">
	import { Switch } from '$lib/components/ui/switch';
	import { Label } from '$lib/components/ui/label';
	import { createEventDispatcher } from 'svelte';

	export let isActive: boolean = false;
	export let isDisabled: boolean = false;
	export let id: string = 'status-toggle';
	export let activeLabel: string = '';
	export let inactiveLabel: string = '';
	export let activeColor: string = 'text-[#0058D6]';
	export let inactiveColor: string = 'text-red-500';
	export let activeBackgroundColor: string = 'data-[state=checked]:bg-[#0058D6]';

	const dispatch = createEventDispatcher<{
		toggle: { checked: boolean };
	}>();

	function handleToggle(checked: boolean) {
		dispatch('toggle', { checked });
	}
</script>

<div class="flex items-center {activeLabel || inactiveLabel ? 'space-x-2' : ''}">
	<Switch
		class={activeBackgroundColor}
		{id}
		checked={isActive}
		disabled={isDisabled}
		onCheckedChange={(checked) => {
			handleToggle(checked);
		}}
	/>
	<Label for={id} class={isActive ? activeColor : inactiveColor}>
		{isActive ? activeLabel : inactiveLabel}
	</Label>
</div>
