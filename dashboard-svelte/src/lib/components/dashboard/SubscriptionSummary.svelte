<script lang="ts">
	import { subscriptionStore } from '$lib/stores/subscription-store';
	import Loading from '$lib/components/Loading.svelte';
	import { onMount } from 'svelte';
	import {
		subscription as subscriptionLabel,
		plan as planLabel,
		term as termLabel,
		tokens_consumed as tokensConsumedLabel,
		channel_slots as channelSlotsLabel,
		days_consumed as daysConsumedLabel,
		failed_to_load_subscription as failedToLoadSubscription
	} from '$lib/paraglide/messages';

	export let onAiId: string;

	let daysRemaining = 0;
	let totalDays = 0;
	let daysUsagePercentage = 0;
	let usedDays = 0;
	let tokensUsed = 0;

	$: tokenBarClass =
		($subscriptionStore.data?.usage_percentage ?? 0) > 80 ? 'bg-destructive' : 'bg-primary';
	$: daysBarClass = daysUsagePercentage > 80 ? 'bg-destructive' : 'bg-primary';

	onMount(() => {
		subscriptionStore.getCurrent(onAiId);
	});

	$: if ($subscriptionStore.data) {
		const start = new Date($subscriptionStore.data.start_date);
		const end = new Date($subscriptionStore.data.end_date);
		const now = new Date();
		totalDays = Math.max(1, Math.ceil((end.getTime() - start.getTime()) / 86400000));
		daysRemaining = Math.max(0, Math.ceil((end.getTime() - now.getTime()) / 86400000));
		usedDays = totalDays - daysRemaining;
		daysUsagePercentage = Math.min(100, Math.max(0, (usedDays / totalDays) * 100));
		tokensUsed =
			$subscriptionStore.data.tokens_used ??
			$subscriptionStore.data.token_limit - $subscriptionStore.data.tokens_remaining;
	}
</script>

{#if $subscriptionStore.loading}
	<Loading />
{:else if $subscriptionStore.data}
	<div class="space-y-4">
		<h2 class="mb-2 lowercase">{subscriptionLabel()}</h2>

		<div class="grid grid-cols-1 gap-4 md:grid-cols-2">
			<div class="bg-card rounded-lg p-4">
				<p class="text-muted-foreground text-sm">{planLabel()}</p>
				<p class="font-medium">{$subscriptionStore.data.plan}</p>
			</div>

			<div class="bg-card rounded-lg p-4">
				<p class="text-muted-foreground text-sm">{termLabel()}</p>
				<p class="font-medium">{$subscriptionStore.data.term}</p>
			</div>

			<div class="bg-card rounded-lg p-4">
				<p class="text-muted-foreground text-sm">{tokensConsumedLabel()}</p>
				<p class="font-medium">
					{tokensUsed} / {$subscriptionStore.data.token_limit}
				</p>
				<div class="relative mt-2 h-2 w-full rounded bg-gray-200">
					<div
						class={'absolute left-0 top-0 h-full rounded ' + tokenBarClass}
						style="width: {$subscriptionStore.data.usage_percentage}%"
					></div>
				</div>
			</div>

			<div class="bg-card rounded-lg p-4">
				<p class="text-muted-foreground text-sm">{channelSlotsLabel()}</p>
				<p class="font-medium">{$subscriptionStore.data.channel_slots} (1 free)</p>
			</div>

			<div class="bg-card rounded-lg p-4">
				<p class="text-muted-foreground text-sm">{daysConsumedLabel()}</p>
				<p class="font-medium">{usedDays} / {totalDays}</p>
				<div class="relative mt-2 h-2 w-full rounded bg-gray-200">
					<div
						class={'absolute left-0 top-0 h-full rounded ' + daysBarClass}
						style="width: {daysUsagePercentage}%"
					></div>
				</div>
			</div>
		</div>
	</div>
{:else}
	<div class="text-destructive lowercase">{failedToLoadSubscription()}</div>
{/if}
