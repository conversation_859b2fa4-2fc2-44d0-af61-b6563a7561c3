<script lang="ts">
	import SubscriptionSummary from '$lib/components/dashboard/SubscriptionSummary.svelte';
	import type { UUID } from '$lib/types/on-ai.types';
	import { SUBSCRIPTION_ENABLED } from '$lib/config/featureFlags';
	import { subscription_disabled } from '$lib/paraglide/messages';

	export let onAiId: UUID;
</script>

<div class="space-y-4">
	{#if SUBSCRIPTION_ENABLED}
		<SubscriptionSummary {onAiId} />
	{:else}
		<div class="text-muted-foreground lowercase">{subscription_disabled()}</div>
	{/if}
</div>
