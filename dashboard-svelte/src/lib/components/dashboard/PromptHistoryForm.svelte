<script lang="ts">
	import { onMount } from 'svelte';
	import { Button } from '$lib/components/ui/button';
	import { formatDate } from '$lib/utils/date';
	import type { PromptType } from '$lib/stores/admin-prompt-store';
	import { type PromptVersion } from '$lib/types/prompt.types';
	import { onAiStore } from '$lib/stores/onai-store';
	import { toast } from 'svelte-sonner';
	import CompareDialog from './CompareDialog.svelte';
	import type { AbstractPromptStore } from '$lib/stores/abstract-prompt-store';
	import { Skeleton } from '$lib/components/ui/skeleton';
	import {
		history_empty,
		knowledge_restore,
		knowledge_compare,
		close,
		no_description
	} from '$lib/paraglide/messages';

	export let type: PromptType;
	export let store: AbstractPromptStore;
	export let maxlength: number;

	export let onRestore: (content: string) => void;
	export let onClose: () => void;

	$: versions = $store[type].versions?.reverse() ?? [];
	$: isLoading = $store[type].areVersionsLoading;

	let showCompare = false;
	let selectedVersionForComparison: number | null = null;

	onMount(() => {
		store.loadVersions(type);
	});

	function getVersionContent(versionNumber: number) {
		return store.getVersionContent(type, versionNumber);
	}

	function restoreVersion(versionNumber: number) {
		return store.restoreVersion(type, versionNumber);
	}

	async function handleRestore(version: PromptVersion) {
		await store.restoreVersion(type, version.version_number);
		onClose();
	}
</script>

<div class="max-h-[60vh] overflow-y-auto">
	{#if isLoading}
		{#each Array(3) as _}
			<div class="mb-4 rounded-lg border p-4">
				<div class="mb-2 flex items-center justify-between">
					<Skeleton class="h-4 w-[100px]" />
					<div class="flex gap-2">
						<Skeleton class="h-8 w-[100px]" />
						<Skeleton class="h-8 w-[80px]" />
					</div>
				</div>
				<Skeleton class="h-4 w-full" />
			</div>
		{/each}
	{:else if versions.length === 0}
		<p class="text-center text-sm text-gray-500">{history_empty()}</p>
	{:else}
		{#each versions as version}
			<div class="mb-4 rounded-lg border p-4">
				<div class="mb-2 flex items-center justify-between">
					<span class="text-sm text-gray-500">
						{formatDate(new Date(version.created_at))}
					</span>
					<div class="flex gap-2">
						<Button size="sm" onclick={() => handleRestore(version)}>
							{knowledge_restore()}
						</Button>
						<Button
							variant="outline"
							size="sm"
							onclick={() => {
								selectedVersionForComparison = version.version_number;
								showCompare = true;
							}}
						>
							{knowledge_compare()}
						</Button>
					</div>
				</div>
				<p class="text-sm text-gray-500">{version.change_description || no_description()}</p>
			</div>
		{/each}
	{/if}
</div>
<div class="flex justify-end pt-4">
	<Button variant="outline" onclick={onClose}>{close()}</Button>
</div>

{#if showCompare && selectedVersionForComparison}
	<CompareDialog
		bind:open={showCompare}
		{versions}
		selectedVersion={selectedVersionForComparison}
		getTokenCount={store.getTokenCount}
		{onRestore}
		{maxlength}
		{getVersionContent}
		{restoreVersion}
		enableDiff={true}
	/>
{/if}
