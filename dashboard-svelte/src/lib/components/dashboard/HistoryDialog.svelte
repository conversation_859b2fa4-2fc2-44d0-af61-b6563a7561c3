<script lang="ts">
	import * as Dialog from '$lib/components/ui/dialog';
	import * as Drawer from '$lib/components/ui/drawer';
	import { useResponsive } from '$lib/utils/responsive';

	export let open = false;
	export let title: string;
	export let description: string;

	const { isMobile } = useResponsive();
	$: isDesktop = !$isMobile;
</script>

{#if isDesktop}
	<Dialog.Root bind:open>
		<Dialog.Content class="sm:max-w-[425px]">
			<Dialog.Header>
				<Dialog.Title>{title}</Dialog.Title>
				<Dialog.Description>{description}</Dialog.Description>
			</Dialog.Header>
			<slot />
		</Dialog.Content>
	</Dialog.Root>
{:else}
	<Drawer.Root bind:open>
		<Drawer.Content class="flex h-[90vh] flex-col">
			<Drawer.Header class="text-left">
				<Drawer.Title>{title}</Drawer.Title>
				<Drawer.Description>{description}</Drawer.Description>
			</Drawer.Header>
			<div class="flex-1 overflow-auto px-4">
				<slot />
			</div>
		</Drawer.Content>
	</Drawer.Root>
{/if}
