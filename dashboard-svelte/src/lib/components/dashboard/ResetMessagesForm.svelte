<script lang="ts">
	import { Button } from '$lib/components/ui/button';
	import {
		cancel,
		users_reset_confirm,
		users_reset_button,
		users_reset_all_button
	} from '$lib/paraglide/messages';
	import { ResetAction } from '$lib/constants/reset-actions';
	import { analytics } from '$lib/services/analytics';

	export let onClose: () => void;
	export let onConfirm: () => void;
	export let action: ResetAction | null = null;
	export let userId: string = '';
	export let isProcessing: boolean = false;

	function getActionTitle() {
		switch (action) {
			case ResetAction.Reset:
				return users_reset_button();
			case ResetAction.ResetAll:
				return users_reset_all_button();
			default:
				return '';
		}
	}

	function getConfirmMessage() {
		if (userId) {
			return `${users_reset_confirm()} ${action === ResetAction.Reset ? users_reset_button() : users_reset_all_button()} ${userId}?`;
		} else {
			return `${users_reset_confirm()} ${action === ResetAction.ResetAll ? users_reset_all_button() : users_reset_button()}?`;
		}
	}

	function handleCancel() {
		analytics.trackButtonClicked('cancel_reset_messages', 'reset_messages_form', 'outline');
		onClose();
	}

	function handleConfirm() {
		const actionType = action === ResetAction.ResetAll ? 'reset_all' : 'reset_single';
		analytics.trackButtonClicked('confirm_reset_messages', 'reset_messages_form', 'destructive');
		analytics.trackMessageReset(userId || 'all', 0); // Count will be tracked in the actual implementation
		onConfirm();
	}
</script>

<div>
	<p class="text-muted-foreground text-sm">
		{getConfirmMessage()}
	</p>
	<div class="flex justify-end gap-3 pt-4">
		<Button variant="outline" onclick={handleCancel} disabled={isProcessing}>
			{cancel()}
		</Button>
		<Button variant="destructive" onclick={handleConfirm} disabled={isProcessing}>
			{getActionTitle()}
		</Button>
	</div>
</div>
