<script lang="ts">
	import { Button } from '$lib/components/ui/button';
	import { toast } from 'svelte-sonner';

	export const onaiId: string = '';
	export let onClose: () => void;

	let isLoading = false;
	let appId = '';
	let secretKey = '';

	async function handleConnect() {
		if (!appId || !secretKey) {
			toast.error('Please enter both App ID and Secret Key');
			return;
		}

		isLoading = true;
		try {
			// TODO: Implement ChatApp connection
			toast.success('Successfully connected to ChatApp');
			onClose();
		} catch (error) {
			toast.error('Failed to connect to ChatApp');
		} finally {
			isLoading = false;
		}
	}
</script>

<div class="flex flex-col items-center gap-4">
	<p class="text-muted-foreground text-center text-sm">Для подключения ChatApp:</p>
	<input
		type="text"
		placeholder="email"
		class="mb-2 w-full rounded border p-2"
		bind:value={appId}
	/>
	<input
		type="text"
		placeholder="password"
		class="w-full rounded border p-2"
		bind:value={secretKey}
	/>
	<Button variant="outline" onclick={handleConnect} disabled={isLoading}>
		{isLoading ? 'Подключение...' : 'Подключить ChatApp'}
	</Button>
</div>
