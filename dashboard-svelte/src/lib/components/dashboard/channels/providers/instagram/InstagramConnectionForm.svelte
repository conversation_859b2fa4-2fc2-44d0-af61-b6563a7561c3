<script lang="ts">
	import { Button } from '$lib/components/ui/button';
	import { toast } from 'svelte-sonner';

	export const onaiId: string = '';
	export let onClose: () => void;

	let isLoading = false;

	async function handleConnect() {
		isLoading = true;
		try {
			// TODO: Implement Instagram OAuth flow
			toast.success('Successfully connected to Instagram');
			onClose();
		} catch (error) {
			toast.error('Failed to connect to Instagram');
		} finally {
			isLoading = false;
		}
	}
</script>

<div class="flex flex-col items-center gap-4">
	<p class="text-muted-foreground text-center text-sm">Для подключения Instagram:</p>
	<Button variant="outline" onclick={handleConnect} disabled={isLoading}>
		{isLoading ? 'Подключение...' : 'Войти через Instagram'}
	</Button>
</div>
