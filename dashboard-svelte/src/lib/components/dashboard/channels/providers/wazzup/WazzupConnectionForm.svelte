<script lang="ts">
	import { Button } from '$lib/components/ui/button';
	import { toast } from 'svelte-sonner';

	export const onaiId: string = '';
	export let onClose: () => void;

	let isLoading = false;
	let apiKey = '';

	async function handleConnect() {
		if (!apiKey) {
			toast.error('Please enter API key');
			return;
		}

		isLoading = true;
		try {
			// TODO: Implement Wazzup connection
			toast.success('Successfully connected to Wazzup');
			onClose();
		} catch (error) {
			toast.error('Failed to connect to <PERSON>azzu<PERSON>');
		} finally {
			isLoading = false;
		}
	}
</script>

<div class="flex flex-col items-center gap-4">
	<p class="text-muted-foreground text-center text-sm">Для подключения Wazzup:</p>
	<input
		type="text"
		placeholder="API ключ Wazzup"
		class="w-full rounded border p-2"
		bind:value={api<PERSON><PERSON>}
	/>
	<Button variant="outline" onclick={handleConnect} disabled={isLoading}>
		{isLoading ? 'Подключение...' : 'Подключить Wazzup'}
	</Button>
</div>
