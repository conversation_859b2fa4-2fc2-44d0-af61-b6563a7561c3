<script lang="ts">
	import { Button } from '$lib/components/ui/button';
	import type { ChannelResponse } from '$lib/types/channel.types';
	import WazzupConnectionForm from './WazzupConnectionForm.svelte';
	import {
		channel_settings_save,
		channel_settings_saving,
		channel_settings_success,
		channel_settings_failed,
		cancel
	} from '$lib/paraglide/messages';
	import { toast } from 'svelte-sonner';

	export let channel: ChannelResponse;
	export let onClose: () => void;

	let isSaving = false;

	async function handleSave() {
		try {
			isSaving = true;
			toast.success(channel_settings_success());
			onClose();
		} catch (error) {
			console.error('Failed to update wazzup settings:', error);
			toast.error(channel_settings_failed());
		} finally {
			isSaving = false;
		}
	}
</script>

<div class="space-y-4">
	<div class="">
		<WazzupConnectionForm onaiId={channel.on_ai_id} {onClose} />
	</div>

	<div class="flex justify-end gap-3 pt-4">
		<Button variant="outline" onclick={onClose} disabled={isSaving}>
			{cancel()}
		</Button>
		<Button variant="default" onclick={handleSave} disabled={isSaving}>
			{isSaving ? channel_settings_saving() : channel_settings_save()}
		</Button>
	</div>
</div>
