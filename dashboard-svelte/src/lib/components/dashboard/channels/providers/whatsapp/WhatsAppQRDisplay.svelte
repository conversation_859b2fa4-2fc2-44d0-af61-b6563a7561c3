<script lang="ts">
	import { whatsappUIStore } from '$lib/stores/whatsapp-ui-store';
	import type { UUID, QRCode } from '$lib/types/whatsapp.types';
	import * as logsAPI from '@opentelemetry/api-logs';
	import { SERVICE_NAME } from '$lib/constants/telemetry';

	const logger = logsAPI.logs.getLogger(SERVICE_NAME);

	export let onaiId: UUID;
	export let whatsappId: UUID;
	export let qrCode: QRCode | null = null;

	const uiState = whatsappUIStore.getUIState(whatsappId);

	let refreshing = false;

	async function refreshQrCode() {
		refreshing = true;
		try {
			await whatsappUIStore.requestQR(onaiId, whatsappId);
		} catch (error) {
			logger.emit({
				body: `[WhatsApp] Error refreshing QR code: ${error instanceof Error ? error.message : String(error)}`,
				severityNumber: logsAPI.SeverityNumber.ERROR,
				severityText: 'ERROR'
			});
		} finally {
			refreshing = false;
		}
	}
</script>

<div class="flex flex-col items-center space-y-4">
	{#if qrCode?.base64}
		<img src={qrCode.base64} alt="whatsapp qr-код" class="h-64 w-64 rounded-lg border" />
		<p class="mt-2 text-xs text-gray-500">
			qr-код действителен еще {$uiState.remainingSeconds ?? 0} сек
		</p>
		<button
			class="mt-2 rounded bg-blue-500 px-4 py-2 text-white hover:bg-blue-600 disabled:opacity-50"
			onclick={refreshQrCode}
			disabled={refreshing}
		>
			{refreshing ? 'обновление...' : 'обновить qr-код'}
		</button>
	{:else}
		<div class="h-64 w-64 animate-pulse rounded-lg bg-gray-200"></div>
		<p class="text-sm text-gray-500">загрузка qr-кода...</p>
	{/if}
</div>
