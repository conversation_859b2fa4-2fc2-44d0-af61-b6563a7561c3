<script lang="ts">
	import { whatsappStore } from '$lib/stores/whatsapp-store';
	import { onMount, onDestroy } from 'svelte';
	import { dev } from '$app/environment';
	import type { QRCode as QRCodeType } from '@/types/whatsapp.types';
	import type { WhatsAppChannelStatusResponse } from '@/types/whatsapp.types';
	import WhatsAppUnauthenticatedForm from './WhatsAppUnauthenticatedForm.svelte';
	import WhatsAppAuthenticatedForm from './WhatsAppAuthenticatedForm.svelte';
	import { Progress } from '$lib/components/ui/progress';

	export let onaiId: string;
	export let whatsappId: string = '';
	export let qrCode: QRCodeType | null = null;
	export let status: WhatsAppChannelStatusResponse | null = null;
	export let onClose: () => void;

	let progress = 50;
	let progressInterval: ReturnType<typeof setInterval>;

	onMount(() => {
		progressInterval = setInterval(() => {
			progress = Math.min(progress + 1, 100);
		}, 100);
	});

	let isInitialized = false;

	$: channelState = $whatsappStore.channels.get(whatsappId);
	$: isAuthenticated = channelState?.status?.text === 'AUTH';
	$: isLoading = !channelState || !channelState.status;

	$: {
		if (!isInitialized && (status || qrCode)) {
			whatsappStore.initialize(whatsappId);
			isInitialized = true;
		}
	}

	onMount(() => {
		if (whatsappId && onaiId) {
			whatsappStore.fetchStatus(onaiId, whatsappId);
		}
	});

	onDestroy(() => {
		clearInterval(progressInterval);
	});
</script>

<div class="flex flex-col gap-4">
	{#if dev}
		<details class="overflow-x-auto text-wrap text-sm text-gray-500">
			<summary>channelstate</summary>
			{JSON.stringify(channelState, null, 2)}
		</details>
	{/if}

	{#if isLoading}
		<div class="flex flex-col items-center gap-4">
			<Progress value={progress} class="w-full" />
		</div>
	{:else if !isAuthenticated}
		<WhatsAppUnauthenticatedForm {onaiId} {whatsappId} on:success={onClose} />
	{:else}
		<WhatsAppAuthenticatedForm {onaiId} {whatsappId} on:close={onClose} />
	{/if}
</div>
