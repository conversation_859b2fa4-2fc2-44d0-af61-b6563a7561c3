<script lang="ts">
	import { Button } from '$lib/components/ui/button';
	import { Skeleton } from '$lib/components/ui/skeleton';
	import { whatsappStore } from '$lib/stores/whatsapp-store';
	import { whatsappUIStore } from '$lib/stores/whatsapp-ui-store';
	import { createEventDispatcher, onDestroy } from 'svelte';

	export let onaiId: string;
	export let whatsappId: string;
	const uiState = whatsappUIStore.getUIState(whatsappId);

	const dispatch = createEventDispatcher<{
		success: void;
		error: { message: string };
	}>();

	$: channelState = $whatsappStore.channels.get(whatsappId);
	$: if (channelState?.status?.text === 'AUTH') {
		dispatch('success');
	}

	onDestroy(() => {
		whatsappUIStore.stopQRCountdown(whatsappId);
	});

	async function handleGetQR() {
		try {
			const response = await whatsappUIStore.requestQR(onaiId, whatsappId);
			if (!response.qr_code) {
				dispatch('error', { message: 'не удалось получить qr-код' });
			}
		} catch (error) {
			console.error('QR error:', error);
			dispatch('error', { message: 'не удалось получить qr-код' });
		}
	}
</script>

<div class="flex flex-col items-center gap-4">
	{#if !$uiState.hasRequestedQR}
		<p class="text-muted-foreground text-center text-sm">
			нажмите кнопку ниже, чтобы получить qr-код для подключения whatsapp
		</p>
		<Button onclick={handleGetQR} disabled={$uiState.isLoading} class="w-full">
			{$uiState.isLoading ? 'загрузка...' : 'получить qr-код'}
		</Button>
	{:else}
		<p class="text-muted-foreground text-center text-sm">
			отсканируйте qr-код в приложении whatsapp для подключения
		</p>
		{#if channelState && channelState.type === 'qr' && channelState.qrCode?.base64 && !$uiState.isLoading}
			<img src={channelState.qrCode.base64} alt="qr-код whatsapp" class="h-64 w-64" />
			<p class="text-xs text-gray-500">
				qr-код действителен еще {$uiState.remainingSeconds ?? 0} сек
			</p>
		{:else}
			<Skeleton class="h-64 w-64" />
		{/if}
		<Button onclick={handleGetQR} disabled={$uiState.isLoading} class="w-full">
			{$uiState.isLoading ? 'загрузка...' : 'обновить qr-код'}
		</Button>
	{/if}
</div>
