<script lang="ts">
	import type { ConnectionType } from '@/types/whatsapp.types';
	import * as Tabs from '$lib/components/ui/tabs';
	import { whatsappUIStore } from '$lib/stores/whatsapp-ui-store';
	import { whatsappStore } from '$lib/stores/whatsapp-store';
	import WhatsAppQRScanner from './WhatsAppQRScanner.svelte';
	import WhatsAppPhoneForm from './WhatsAppPhoneForm.svelte';
	import { qr_code, phone_number } from '$lib/paraglide/messages';
	import { toast } from 'svelte-sonner';
	import { createEventDispatcher } from 'svelte';
	import * as logsAPI from '@opentelemetry/api-logs';
	import { SERVICE_NAME } from '$lib/constants/telemetry';

	const logger = logsAPI.logs.getLogger(SERVICE_NAME);

	export let onaiId: string;
	export let whatsappId: string;

	let connectionMethod: ConnectionType = 'qr';

	function handleConnectionSuccess() {
		logger.emit({
			body: `[WhatsAppUnauthenticatedForm] WhatsApp connection successful: ${whatsappId} via ${connectionMethod}`,
			severityNumber: logsAPI.SeverityNumber.INFO,
			severityText: 'INFO',
			attributes: {
				onaiId,
				whatsappId,
				connectionMethod
			}
		});

		toast.success('whatsapp успешно подключен');
		dispatch('success');
	}

	function handleConnectionError(event: CustomEvent<{ message: string }>) {
		const errorMessage = event.detail.message || 'Неизвестная ошибка при подключении WhatsApp';

		// Log the error with OpenTelemetry
		logger.emit({
			body: `[WhatsAppUnauthenticatedForm] WhatsApp connection error: ${errorMessage}`,
			severityNumber: logsAPI.SeverityNumber.ERROR,
			severityText: 'ERROR',
			attributes: {
				onaiId,
				whatsappId,
				connectionMethod,
				errorMessage
			}
		});

		// Log to console for additional debugging
		console.error('WhatsApp Connection Error:', errorMessage);

		// Show user-friendly toast notification
		toast.error(errorMessage);

		// Dispatch an error event for parent components if needed
		dispatch('error', { message: errorMessage });
	}

	const dispatch = createEventDispatcher<{
		success: void;
		error: { message: string };
	}>();

	// Log component initialization
	logger.emit({
		body: `[WhatsAppUnauthenticatedForm] Initialized for WhatsApp connection`,
		severityNumber: logsAPI.SeverityNumber.DEBUG,
		severityText: 'DEBUG',
		attributes: {
			onaiId,
			whatsappId,
			initialConnectionMethod: connectionMethod
		}
	});
</script>

<Tabs.Root
	value={connectionMethod}
	onValueChange={(value: string) => {
		const previousMethod = connectionMethod;
		connectionMethod = value as ConnectionType;

		// cleanup previous method state to avoid cross-interference
		whatsappUIStore.reset(whatsappId);
		if (previousMethod === 'phone') {
			// additionally clear auth-related channel state when leaving phone method
			whatsappStore.resetChannel(whatsappId);
		}

		logger.emit({
			body: `[WhatsAppUnauthenticatedForm] Connection method changed`,
			severityNumber: logsAPI.SeverityNumber.INFO,
			severityText: 'INFO',
			attributes: {
				onaiId,
				whatsappId,
				previousMethod,
				newMethod: connectionMethod
			}
		});
	}}
>
	<Tabs.List class="grid w-full grid-cols-2 gap-2">
		<Tabs.Trigger value="qr">
			{qr_code()}
		</Tabs.Trigger>
		<Tabs.Trigger value="phone">
			{phone_number()}
		</Tabs.Trigger>
	</Tabs.List>

	<Tabs.Content value="qr" class="mt-4">
		<WhatsAppQRScanner
			{onaiId}
			{whatsappId}
			on:success={handleConnectionSuccess}
			on:error={handleConnectionError}
		/>
	</Tabs.Content>

	<Tabs.Content value="phone" class="mt-4">
		<WhatsAppPhoneForm
			{onaiId}
			{whatsappId}
			on:success={handleConnectionSuccess}
			on:error={handleConnectionError}
		/>
	</Tabs.Content>
</Tabs.Root>
