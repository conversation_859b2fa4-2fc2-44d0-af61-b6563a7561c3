<script lang="ts">
	import { But<PERSON> } from '$lib/components/ui/button';
	import { toast } from 'svelte-sonner';
	import LucideCheckCircle from '~icons/lucide/check-circle';
	import { whatsappUIStore } from '$lib/stores/whatsapp-ui-store';
	import { createEventDispatcher } from 'svelte';
	import * as logsAPI from '@opentelemetry/api-logs';
	import { SERVICE_NAME } from '$lib/constants/telemetry';
	import * as api from '@opentelemetry/api';
	import { whatsappStore } from '$lib/stores/whatsapp-store';

	const logger = logsAPI.logs.getLogger(SERVICE_NAME);
	const tracer = api.trace.getTracer(SERVICE_NAME);

	export let onaiId: string;
	export let whatsappId: string;

	$: channelState = $whatsappStore.channels.get(whatsappId);
	$: channelUserDisplay = channelState?.user
		? [channelState.user.name, channelState.user.id].filter(Boolean).join(' - ')
		: '';

	const dispatch = createEventDispatcher<{
		close: void;
	}>();

	$: uiState = whatsappUIStore.getUIState(whatsappId);

	async function handleDisconnect() {
		return tracer.startActiveSpan('whatsapp.disconnect', async (span) => {
			try {
				// Log disconnect attempt
				logger.emit({
					body: `[WhatsAppAuthenticatedForm] Attempting to disconnect WhatsApp channel`,
					severityNumber: logsAPI.SeverityNumber.INFO,
					severityText: 'INFO',
					attributes: {
						whatsappId,
						channelUserDisplay
					}
				});

				await whatsappUIStore.disconnect(onaiId, whatsappId);

				// Log successful disconnect
				logger.emit({
					body: `[WhatsAppAuthenticatedForm] WhatsApp channel disconnected successfully`,
					severityNumber: logsAPI.SeverityNumber.INFO,
					severityText: 'INFO',
					attributes: {
						whatsappId,
						channelUserDisplay
					}
				});

				toast.success('успешно отключено от whatsapp');
				dispatch('close');
			} catch (error) {
				const err = error instanceof Error ? error : new Error(String(error));
				span.recordException(err);

				logger.emit({
					body: `[WhatsAppAuthenticatedForm] Error disconnecting WhatsApp channel`,
					severityNumber: logsAPI.SeverityNumber.ERROR,
					severityText: 'ERROR',
					attributes: {
						whatsappId,
						channelUserDisplay,
						errorMessage: err.message
					}
				});

				toast.error('не удалось отключиться от whatsapp');
			} finally {
				span.end();
			}
		});
	}

	// Component initialization span
	tracer.startActiveSpan('whatsapp.authenticated_form.init', (span) => {
		// Log component initialization
		logger.emit({
			body: `[WhatsAppAuthenticatedForm] Initialized for authenticated WhatsApp channel`,
			severityNumber: logsAPI.SeverityNumber.DEBUG,
			severityText: 'DEBUG',
			attributes: {
				whatsappId,
				channelUserDisplay
			}
		});
		span.end();
	});
</script>

<div class="flex flex-col items-center gap-4">
	<div class="flex items-center text-green-600">
		<LucideCheckCircle class="mr-2 h-5 w-5" />
		whatsapp успешно подключен
	</div>
	<p class="text-muted-foreground text-center text-sm">
		подключено к whatsapp как {channelUserDisplay}
	</p>
	<Button variant="outline" onclick={handleDisconnect} disabled={$uiState.isLoading} class="w-full">
		{$uiState.isLoading ? 'отключение...' : 'отключить'}
	</Button>
</div>
