<script lang="ts">
	import { createStatusColorStore, createStatusTextStore } from '$lib/stores/whatsapp-store';
	import type { UUID } from '$lib/types/whatsapp.types';

	export let whatsappId: UUID;
	export let isLoading = false;

	const statusColor = createStatusColorStore(whatsappId);
	const statusText = createStatusTextStore(whatsappId);
</script>

<div class="mb-4 flex items-center">
	<div class={`mr-2 h-3 w-3 rounded-full ${$statusColor}`}></div>
	<span>
		{#if isLoading}
			подключение...
		{:else}
			{$statusText}
		{/if}
	</span>
</div>
