<script lang="ts">
	import LucideCheckCircle from '~icons/lucide/check-circle';
	import type { WAContact } from '$lib/types/whatsapp.types';

	export let user: WAContact | null = null;
</script>

<div class="flex flex-col space-y-2">
	<div class="flex items-center text-green-600">
		<LucideCheckCircle class="mr-2 h-5 w-5" />
		whatsapp успешно подключен
	</div>
	{#if user?.pushname}
		<p class="text-sm text-gray-600">
			подключено к whatsapp как {user.pushname}
		</p>
	{/if}
</div>
