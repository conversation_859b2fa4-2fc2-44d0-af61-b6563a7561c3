<script lang="ts">
	import type { CountryCode, E164Number } from 'svelte-tel-input/types';
	import { Button } from '$lib/components/ui/button';
	import { createEventDispatcher } from 'svelte';
	import { loading } from '$lib/paraglide/messages';
	import { toast } from 'svelte-sonner';
	import { whatsappStore } from '$lib/stores/whatsapp-store';
	import PhoneInputField from '$lib/components/ui/phone-input/PhoneInputField.svelte';

	const dispatch = createEventDispatcher<{
		success: void;
		error: { message: string };
	}>();

	export let onaiId: string;
	export let isSubmitting = false;
	export let errorMessage = '';
	export let whatsappId: string;

	let selectedCountry: CountryCode = 'KZ';
	let phoneValid = false;
	let phone: E164Number | null = '';
	let showAuthCode = false;
	let isUpdatingCode = false;

	$: channelState = $whatsappStore.channels.get(whatsappId);
	$: if (showAuthCode && !(channelState && channelState.type === 'auth' && channelState.authCode)) {
		showAuthCode = false;
	}

	async function handleSubmit(event: Event, isUpdate: boolean = false) {
		event.preventDefault();
		if (!phoneValid || !phone) {
			toast.error('пожалуйста, введите корректный номер телефона');
			return;
		}

		isSubmitting = true;
		try {
			await whatsappStore.connectByPhone(onaiId, whatsappId, phone);
			showAuthCode = true;
			if (isUpdate) {
				toast.success('код авторизации успешно обновлен');
			}
		} catch (error) {
			console.error('failed to connect whatsapp by phone:', error);
			const errorMessage = error instanceof Error ? error.message : 'failed to connect whatsapp';
			toast.error('не удалось подключить whatsapp по номеру телефона');
			dispatch('error', { message: errorMessage });
		} finally {
			isSubmitting = false;
		}
	}

	function handleContinue() {
		dispatch('success');
	}

	async function handleUpdateCode(event: Event) {
		isUpdatingCode = true;
		await handleSubmit(event, true);
		isUpdatingCode = false;
	}
</script>

{#if !showAuthCode}
	<form onsubmit={(e) => handleSubmit(e)} class="space-y-6">
		<PhoneInputField
			bind:selectedCountry
			bind:phone
			bind:phoneValid
			bind:errorMessage
			autofocus={true}
			disabled={isSubmitting}
		/>

		<Button type="submit" class="w-full" disabled={isSubmitting || !phoneValid} variant="default">
			{#if isSubmitting}
				<div class="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-b-transparent"></div>
				{loading()}
			{:else}
				подключить whatsapp
			{/if}
		</Button>
	</form>
{:else}
	<div class="space-y-6">
		<div class="rounded-lg border bg-amber-50 p-6 text-center">
			<h4 class="mb-3 font-medium text-amber-800">код авторизации</h4>
			{#if channelState && channelState.type === 'auth'}
				<div class="mb-3 flex justify-center">
					<span
						class="inline-block rounded-md bg-white px-4 py-2 font-mono text-xl font-bold text-gray-900 shadow-sm dark:bg-gray-800 dark:text-gray-100"
						>{channelState.authCode}</span
					>
				</div>
				<p class="text-sm text-amber-700">используйте данный код для входа в whatsapp</p>
			{/if}
		</div>
		<Button onclick={handleContinue} class="w-full" variant="default">продолжить</Button>
		<Button onclick={handleUpdateCode} class="w-full" variant="outline" disabled={isUpdatingCode}>
			{#if isUpdatingCode}
				<div class="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-b-transparent"></div>
				{loading()}
			{:else}
				обновить код
			{/if}
		</Button>
	</div>
{/if}
