<script lang="ts">
	import { Button } from '$lib/components/ui/button';
	import type { ChannelResponse } from '$lib/types/channel.types';
	import { whatsappStore } from '$lib/stores/whatsapp-store';
	import WhatsAppConnectionForm from './WhatsAppConnectionForm.svelte';
	import {
		channel_settings_save,
		channel_settings_saving,
		channel_settings_success,
		channel_settings_failed,
		cancel
	} from '$lib/paraglide/messages';
	import { toast } from 'svelte-sonner';

	export let channel: ChannelResponse;
	export let onClose: () => void;

	let isSaving = false;
</script>

<div class="space-y-4">
	<WhatsAppConnectionForm onaiId={channel.on_ai_id} whatsappId={channel.id} {onClose} />
</div>
