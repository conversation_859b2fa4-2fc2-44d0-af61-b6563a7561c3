<script lang="ts">
	import { onMount, onDestroy, createEventDispatcher } from 'svelte';
	import { whatsappStore } from '$lib/stores/whatsapp-store';
	import { whatsappUIStore } from '$lib/stores/whatsapp-ui-store';
	import type {
		WhatsAppChannelStatus,
		WhatsAppChannelStatusResponse,
		QRCode
	} from '$lib/types/whatsapp.types';
	import RiWhatsappFill from '~icons/ri/whatsapp-fill';
	import * as logsAPI from '@opentelemetry/api-logs';
	import { SERVICE_NAME } from '$lib/constants/telemetry';

	import WhatsAppStatusIndicator from './WhatsAppStatusIndicator.svelte';
	import WhatsAppQRDisplay from './WhatsAppQRDisplay.svelte';
	import WhatsAppAuthenticatedDisplay from './WhatsAppAuthenticatedDisplay.svelte';

	const logger = logsAPI.logs.getLogger(SERVICE_NAME);

	export let onaiId: string;
	export let whatsappId: string;
	export let initialStatus: WhatsAppChannelStatus | null = null;
	export let initialQrCode: QRCode | null = null;

	const dispatch = createEventDispatcher<{
		statusChange: { status: string };
	}>();

	let isInitialized = false;

	$: channelState = $whatsappStore.channels.get(whatsappId);

	$: {
		if (!isInitialized && (initialStatus || initialQrCode)) {
			logger.emit({
				body: `[WhatsApp] Initializing with props`,
				severityNumber: logsAPI.SeverityNumber.INFO,
				severityText: 'INFO'
			});
			let channelInitStatus: WhatsAppChannelStatus | null = null;

			if (initialStatus && typeof initialStatus !== 'string') {
				channelInitStatus = {
					id: initialStatus.id || '', // Ensure id is always set
					name: initialStatus.name || '', // Ensure name is always set
					onai_id: initialStatus.onai_id || onaiId, // Use onaiId as fallback
					status: initialStatus.status,
					user: initialStatus.user
				};
			}

			whatsappStore.initialize(whatsappId, channelInitStatus);
			isInitialized = true;
		}
	}

	onDestroy(() => {
		logger.emit({
			body: '[WhatsApp] Component destroyed, stopping polling',
			severityNumber: logsAPI.SeverityNumber.INFO,
			severityText: 'INFO'
		});
		whatsappStore.stopStatusPolling(whatsappId);
		whatsappUIStore.stopQRCountdown(whatsappId);
	});
</script>

<div class="whatsapp-status-container mb-4 rounded-lg border p-4">
	<h3 class="mb-2 flex items-center text-lg font-semibold">
		<RiWhatsappFill class="mr-2 h-5 w-5" />
		статус whatsapp
	</h3>

	{#if channelState?.loading}
		<div class="flex justify-center py-4">
			<div class="h-8 w-8 animate-spin rounded-full border-b-2 border-blue-500"></div>
		</div>
	{:else if channelState?.error}
		<div class="py-2 text-red-500">
			не удалось загрузить статус whatsapp.
			<button
				class="ml-2 text-blue-500 underline"
				onclick={() => whatsappStore.fetchStatus(onaiId, whatsappId)}
			>
				повторить
			</button>
		</div>
	{:else}
		<WhatsAppStatusIndicator {whatsappId} isLoading={channelState?.loading || false} />

		{#if channelState?.status?.text === 'QR'}
			{#if channelState && channelState.type === 'qr'}
				<WhatsAppQRDisplay {onaiId} {whatsappId} qrCode={channelState.qrCode} />
			{/if}
		{:else if channelState?.status?.text === 'AUTH'}
			<WhatsAppAuthenticatedDisplay user={channelState?.channel?.user || null} />
		{/if}
	{/if}
</div>
