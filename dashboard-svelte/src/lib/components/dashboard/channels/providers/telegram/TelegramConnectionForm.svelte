<script lang="ts">
	import { Button } from '$lib/components/ui/button';
	import { toast } from 'svelte-sonner';

	export const onaiId: string = '';
	export let onClose: () => void;

	let isLoading = false;
	let botToken = '';

	async function handleConnect() {
		if (!botToken) {
			toast.error('Please enter bot token');
			return;
		}

		isLoading = true;
		try {
			// TODO: Implement telegram bot connection
			toast.success('Successfully connected to Telegram');
			onClose();
		} catch (error) {
			toast.error('Failed to connect to Telegram');
		} finally {
			isLoading = false;
		}
	}
</script>

<div class="flex flex-col items-center gap-4">
	<p class="text-muted-foreground text-center text-sm">Для подключения Telegram бота:</p>
	<ol class="list-decimal space-y-2 pl-4 text-sm">
		<li>Создайте нового бота через @BotFather</li>
		<li>Получите токен бота</li>
		<li>Введите токен ниже</li>
	</ol>
	<input
		type="text"
		placeholder="Введите токен бота"
		class="w-full rounded border p-2"
		bind:value={botToken}
	/>
	<Button variant="outline" onclick={handleConnect} disabled={isLoading}>
		{isLoading ? 'Подключение...' : 'Подключить бота'}
	</Button>
</div>
