<script lang="ts">
	import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '$lib/components/ui/card';
	import * as Tabs from '$lib/components/ui/tabs';
	import WhatsAppStatus from '@/components/dashboard/channels/WhatsAppStatus.svelte';
	import { whatsapp_connection } from '$lib/paraglide/messages';

	export let onaiId: string;
	export let whatsappId: string;
</script>

<Tabs.Content value="qr-scan">
	<Card>
		<CardHeader>
			<CardTitle class="text-center">{whatsapp_connection()}</CardTitle>
		</CardHeader>
		<CardContent>
			<div class="flex flex-col items-center">
				<p class="mb-4 text-center text-gray-600">
					Для использования on.ai необходимо подключить WhatsApp. Пожалуйста, отсканируйте QR-код в
					приложении WhatsApp.
				</p>
				<WhatsAppStatus {onaiId} {whatsappId} on:statusChange />
			</div>
		</CardContent>
	</Card>
</Tabs.Content>
