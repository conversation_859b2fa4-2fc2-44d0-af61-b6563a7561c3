<script lang="ts">
	import type { TransporterType } from '$lib/types/channel.types';
	import type { QRCode as QRCodeType } from '@/types/whatsapp.types';
	import type { WhatsAppChannelStatusResponse } from '@/types/whatsapp.types';
	import WhatsAppConnectionForm from '$lib/components/dashboard/channels/providers/whatsapp/WhatsAppConnectionForm.svelte';
	import TelegramConnectionForm from '$lib/components/dashboard/channels/providers/telegram/TelegramConnectionForm.svelte';
	import InstagramConnectionForm from '$lib/components/dashboard/channels/providers/instagram/InstagramConnectionForm.svelte';
	import WazzupConnectionForm from '$lib/components/dashboard/channels/providers/wazzup/WazzupConnectionForm.svelte';
	import ChatAppConnectionForm from '$lib/components/dashboard/channels/providers/chatapp/ChatAppConnectionForm.svelte';

	export let channelType: TransporterType;
	export let onaiId: string;
	export let qrCode: QRCodeType | null = null;
	export let status: WhatsAppChannelStatusResponse | null = null;
	export let onClose: () => void;
</script>

<div class="flex flex-col gap-4">
	{#if channelType === 'whatsapp'}
		<WhatsAppConnectionForm {onaiId} {qrCode} {status} {onClose} />
	{:else if channelType === 'telegram'}
		<TelegramConnectionForm {onaiId} {onClose} />
	{:else if channelType === 'instagram'}
		<InstagramConnectionForm {onaiId} {onClose} />
	{:else if channelType === 'wazzup'}
		<WazzupConnectionForm {onaiId} {onClose} />
	{:else if channelType === 'chatapp'}
		<ChatAppConnectionForm {onaiId} {onClose} />
	{/if}
</div>
