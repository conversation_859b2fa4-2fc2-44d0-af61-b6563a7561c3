<script lang="ts">
	import { type ChannelResponse, TransporterType } from '$lib/types/channel.types';
	import { getChannelTypeDisplay } from '$lib/utils/channel';
	import ChannelIcon from '$lib/components/channel/ChannelIcon.svelte';
	import { channelStore } from '$lib/stores/channel-store';
	import {
		whatsappStore,
		createStatusColorStore,
		createStatusTextStore
	} from '$lib/stores/whatsapp-store';
	import { onMount, onDestroy } from 'svelte';
	import StatusToggle from '$lib/components/dashboard/StatusToggle.svelte';
	import { toast } from 'svelte-sonner';
	import MaterialSymbolsDeleteOutline from '~icons/material-symbols/delete-outline';
	import MaterialSymbolsSettingsOutlineRounded from '~icons/material-symbols/settings-outline-rounded';
	import ChannelSettingsDialog from '../dialogs/settings/ChannelSettingsDialog.svelte';
	import ChannelDeleteDialog from '../dialogs/delete/ChannelDeleteDialog.svelte';
	import {
		channel_delete,
		channel_delete_failed,
		channel_status_enabled,
		channel_status_disabled,
		channel_status_update_failed,
		channel_enabled,
		channel_disabled,
		channel_name,
		channel_type
	} from '$lib/paraglide/messages';

	export let channel: ChannelResponse;
	let settingsDialogOpen = false;
	let deleteDialogOpen = false;

	$: isWhatsApp = channel.transporter_type === TransporterType.WHATSAPP;

	$: statusColor = isWhatsApp ? createStatusColorStore(channel.id) : null;
	$: statusText = isWhatsApp ? createStatusTextStore(channel.id) : null;
	$: channelState = isWhatsApp ? $whatsappStore.channels.get(channel.id) : null;
	$: channelUserDisplay = channelState?.user
		? [channelState.user.name, channelState.user.id].filter(Boolean).join(' - ')
		: '';

	onMount(() => {
		if (isWhatsApp) {
			whatsappStore.fetchStatus(channel.on_ai_id, channel.id);
		}
	});

	onDestroy(() => {
		if (isWhatsApp) {
			whatsappStore.stopStatusPolling(channel.id);
		}
	});

	async function handleStatusToggle(event: CustomEvent<{ checked: boolean }>) {
		const checked = event.detail.checked;
		try {
			await channelStore.updateChannelStatus(channel.on_ai_id, channel.id, checked);
			toast.success(checked ? channel_status_enabled() : channel_status_disabled());
		} catch (error) {
			console.error('Failed to update channel status:', error);
			toast.error(channel_status_update_failed());
		}
	}

	function openSettings() {
		if (isWhatsApp) {
			whatsappStore.fetchStatus(channel.on_ai_id, channel.id);
		}
		settingsDialogOpen = true;
	}
</script>

<div class="bg-card rounded-lg border p-6 shadow-sm transition-all duration-200">
	<div class="flex items-start gap-4 {!channel.is_enabled ? 'grayscale' : ''}">
		<div class="bg-primary/10 rounded-full p-3">
			<ChannelIcon type={channel.transporter_type} size="lg" />
		</div>
		<div class="flex flex-1 flex-col">
			<div class="flex items-start justify-between">
				<div class="flex flex-col">
					<span class="text-lg font-semibold"
						>{getChannelTypeDisplay(channel.transporter_type)}</span
					>
					{#if isWhatsApp && channelState}
						<span class="text-muted-foreground text-sm">
							{channelUserDisplay}
						</span>
					{/if}
				</div>
				<div class="flex items-center gap-2">
					<button class="text-primary hover:text-primary/90" onclick={openSettings}>
						<span class="sr-only">настройки канала</span>
						<MaterialSymbolsSettingsOutlineRounded class="h-6 w-6" />
					</button>

					<StatusToggle isActive={channel.is_enabled} on:toggle={handleStatusToggle} />
					<button
						class="text-destructive hover:text-destructive/90"
						onclick={() => (deleteDialogOpen = true)}
					>
						<span class="sr-only">{channel_delete()}</span>
						<MaterialSymbolsDeleteOutline class="h-5 w-5" />
					</button>
				</div>
			</div>
			{#if isWhatsApp && channelState}
				<div class="mt-1 flex items-center gap-2">
					<div class={`h-2 w-2 rounded-full ${$statusColor}`}></div>
					<span class="text-sm">{$statusText}</span>
				</div>
			{/if}
		</div>
	</div>
</div>

<ChannelSettingsDialog bind:open={settingsDialogOpen} {channel} />
<ChannelDeleteDialog bind:open={deleteDialogOpen} {channel} />
