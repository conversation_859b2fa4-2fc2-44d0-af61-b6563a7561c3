<script lang="ts">
	import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '$lib/components/ui/card';
	import { Button } from '$lib/components/ui/button';
	import { TransporterType } from '$lib/types/channel.types';
	import ChannelIcon from '$lib/components/channel/ChannelIcon.svelte';
	import AddIcon from '~icons/material-symbols/add';
	import {
		lets_start_with_whatsapp,
		whatsapp_connect_description,
		connect_whatsapp
	} from '$lib/paraglide/messages';
	import LoadingIcon from '~icons/material-symbols/refresh';

	export let onConnect: (type: TransporterType) => Promise<void>;
	export let isChannelProcessing = false;
</script>

<div class="flex min-h-[400px] items-center justify-center">
	<Card class="max-w-md">
		<CardHeader>
			<div class="mb-6 flex justify-center">
				<ChannelIcon type={TransporterType.WHATSAPP} size="lg" />
			</div>
			<CardTitle class="text-center text-lg font-medium">{lets_start_with_whatsapp()}</CardTitle>
		</CardHeader>
		<CardContent class="p-6 text-center">
			<p class="text-muted-foreground mb-6 text-sm lowercase">
				{whatsapp_connect_description()}
			</p>
			<Button
				class="font-medium"
				onclick={() => !isChannelProcessing && onConnect(TransporterType.WHATSAPP)}
				disabled={isChannelProcessing}
			>
				{#if isChannelProcessing}
					<LoadingIcon class="mr-1 h-4 w-4 animate-spin" />
				{:else}
					<AddIcon class="mr-1 h-4 w-4" />
				{/if}
				{connect_whatsapp()}
			</Button>
		</CardContent>
	</Card>
</div>
