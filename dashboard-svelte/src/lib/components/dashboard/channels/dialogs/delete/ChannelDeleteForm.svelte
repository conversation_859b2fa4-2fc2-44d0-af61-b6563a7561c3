<script lang="ts">
	import { Button } from '$lib/components/ui/button';
	import { toast } from 'svelte-sonner';
	import {
		channel_delete,
		channel_delete_failed,
		channel_delete_select_reason,
		channel_delete_reason_not_needed,
		channel_delete_reason_cannot_login,
		channel_deleting,
		cancel
	} from '$lib/paraglide/messages';
	import { channelStore } from '$lib/stores/channel-store';
	import { RadioGroup, RadioGroupItem } from '$lib/components/ui/radio-group';
	import type { ChannelResponse } from '$lib/types/channel.types';

	export let onClose: () => void;
	export let channel: ChannelResponse;

	let selectedReason = 'no_longer_needed';
	let isDeleting = false;

	const reasons = [
		{ value: 'no_longer_needed', label: channel_delete_reason_not_needed() },
		{ value: 'cannot_login', label: channel_delete_reason_cannot_login() }
	];

	async function handleDelete() {
		isDeleting = true;
		try {
			await channelStore.deleteChannel(channel.on_ai_id, channel.id, selectedReason);
			toast.success(channel_delete());
			onClose();
		} catch (error) {
			console.error('Failed to delete channel:', error);
			toast.error(channel_delete_failed());
		} finally {
			isDeleting = false;
		}
	}
</script>

<div>
	<p class="text-muted-foreground text-sm">{channel_delete_select_reason()}</p>
	<RadioGroup bind:value={selectedReason} class="mt-2 space-y-2">
		{#each reasons as reason}
			<label class="flex items-center gap-2">
				<RadioGroupItem value={reason.value} />
				<span class="text-sm">{reason.label}</span>
			</label>
		{/each}
	</RadioGroup>
	<div class="flex justify-end gap-3 pt-4">
		<Button variant="outline" onclick={onClose} disabled={isDeleting}>{cancel()}</Button>
		<Button variant="destructive" onclick={handleDelete} disabled={isDeleting || !selectedReason}>
			{isDeleting ? channel_deleting() : channel_delete()}
		</Button>
	</div>
</div>
