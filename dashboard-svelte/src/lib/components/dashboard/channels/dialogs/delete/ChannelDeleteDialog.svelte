<script lang="ts">
	import * as Dialog from '$lib/components/ui/dialog';
	import * as Drawer from '$lib/components/ui/drawer';
	import { useResponsive } from '$lib/utils/responsive';
	import type { ChannelResponse } from '$lib/types/channel.types';
	import ChannelDeleteForm from './ChannelDeleteForm.svelte';
	import { channel_delete_title } from '$lib/paraglide/messages';

	export let open = false;
	export let channel: ChannelResponse;

	const { isMobile } = useResponsive();
	$: isDesktop = !$isMobile;
</script>

{#if isDesktop}
	<Dialog.Root bind:open>
		<Dialog.Content>
			<Dialog.Header>
				<Dialog.Title>{channel_delete_title()}</Dialog.Title>
			</Dialog.Header>
			<ChannelDeleteForm onClose={() => (open = false)} {channel} />
		</Dialog.Content>
	</Dialog.Root>
{:else}
	<Drawer.Root bind:open>
		<Drawer.Content>
			<Drawer.Header class="text-left">
				<Drawer.Title>{channel_delete_title()}</Drawer.Title>
			</Drawer.Header>
			<div class="px-4">
				<ChannelDeleteForm onClose={() => (open = false)} {channel} />
			</div>
		</Drawer.Content>
	</Drawer.Root>
{/if}
