<script lang="ts">
	import type { ChannelResponse } from '$lib/types/channel.types';
	import { TransporterType } from '$lib/types/channel.types';
	import InstagramSettings from '../../providers/instagram/InstagramSettings.svelte';
	import WhatsAppSettings from '../../providers/whatsapp/WhatsAppSettings.svelte';
	import WazzupSettings from '../../providers/wazzup/WazzupSettings.svelte';
	import ChatAppSettings from '../../providers/chatapp/ChatAppSettings.svelte';
	import TelegramSettings from '../../providers/telegram/TelegramSettings.svelte';

	export let channel: ChannelResponse;
	export let onClose: () => void;
</script>

<div>
	{#if channel.transporter_type === TransporterType.INSTAGRAM}
		<InstagramSettings {channel} {onClose} />
	{:else if channel.transporter_type === TransporterType.WHATSAPP}
		<WhatsAppSettings {channel} {onClose} />
	{:else if channel.transporter_type === TransporterType.WAZZUP}
		<WazzupSettings {channel} {onClose} />
	{:else if channel.transporter_type === TransporterType.CHATAPP}
		<ChatAppSettings {channel} {onClose} />
	{:else if channel.transporter_type === TransporterType.TELEGRAM}
		<TelegramSettings {channel} {onClose} />
	{/if}
</div>
