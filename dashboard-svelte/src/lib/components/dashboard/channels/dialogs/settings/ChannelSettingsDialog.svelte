<script lang="ts">
	import * as Dialog from '$lib/components/ui/dialog';
	import * as Drawer from '$lib/components/ui/drawer';
	import { useResponsive } from '$lib/utils/responsive';
	import { channel_settings } from '$lib/paraglide/messages';
	import ChannelSettingsForm from './ChannelSettingsForm.svelte';
	import type { ChannelResponse } from '$lib/types/channel.types';

	export let open = false;
	export let channel: ChannelResponse;

	const { isMobile } = useResponsive();
	$: isDesktop = !$isMobile;
</script>

{#if isDesktop}
	<Dialog.Root bind:open>
		<Dialog.Content class="max-w-md">
			<Dialog.Header>
				<Dialog.Title>{channel_settings()}</Dialog.Title>
			</Dialog.Header>
			<ChannelSettingsForm {channel} onClose={() => (open = false)} />
		</Dialog.Content>
	</Dialog.Root>
{:else}
	<Drawer.Root bind:open>
		<Drawer.Content>
			<Drawer.Header class="text-left">
				<Drawer.Title>{channel_settings()}</Drawer.Title>
			</Drawer.Header>
			<div class="px-4">
				<ChannelSettingsForm {channel} onClose={() => (open = false)} />
			</div>
		</Drawer.Content>
	</Drawer.Root>
{/if}
