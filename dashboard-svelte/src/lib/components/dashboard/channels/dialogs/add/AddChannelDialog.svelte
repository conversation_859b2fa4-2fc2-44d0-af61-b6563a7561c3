<script lang="ts">
	import * as Dialog from '$lib/components/ui/dialog';
	import * as Drawer from '$lib/components/ui/drawer';
	import { Button } from '$lib/components/ui/button';
	import AddChannelForm from './AddChannelForm.svelte';
	import { useResponsive } from '$lib/utils/responsive';

	export let open = false;

	const { isMobile } = useResponsive();
	$: isDesktop = !$isMobile;
</script>

{#if isDesktop}
	<Dialog.Root bind:open>
		<Dialog.Content class="sm:max-w-[425px]">
			<Dialog.Header>
				<Dialog.Title>Add Channel</Dialog.Title>
				<Dialog.Description>Add a new channel to your ON.AI instance.</Dialog.Description>
			</Dialog.Header>
			<AddChannelForm />
			<Dialog.Footer>
				<Button type="button" variant="outline" onclick={() => (open = false)}>Cancel</Button>
				<Button type="submit" form="add-channel-form">Add Channel</Button>
			</Dialog.Footer>
		</Dialog.Content>
	</Dialog.Root>
{:else}
	<Drawer.Root bind:open>
		<Drawer.Content>
			<Drawer.Header class="text-left">
				<Drawer.Title>Add Channel</Drawer.Title>
				<Drawer.Description>Add a new channel to your ON.AI instance.</Drawer.Description>
			</Drawer.Header>
			<div class="px-4">
				<AddChannelForm />
			</div>
			<Drawer.Footer class="pt-2">
				<div class="flex w-full flex-col gap-2">
					<Button type="submit" class="w-full" form="add-channel-form">Add Channel</Button>
					<Drawer.Close class="w-full">
						<Button type="button" variant="outline" class="w-full">Cancel</Button>
					</Drawer.Close>
				</div>
			</Drawer.Footer>
		</Drawer.Content>
	</Drawer.Root>
{/if}
