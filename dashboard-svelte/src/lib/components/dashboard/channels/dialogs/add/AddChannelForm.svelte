<script lang="ts">
	import { createEventDispatcher } from 'svelte';
	import { Input } from '$lib/components/ui/input';
	import { Label } from '$lib/components/ui/label';
	import { RadioGroup, RadioGroupItem } from '$lib/components/ui/radio-group';
	import { TransporterType } from '$lib/types/channel.types';
	import { CHANNEL_INFO } from '$lib/utils/channel';
	import ChannelIcon from '$lib/components/channel/ChannelIcon.svelte';
	import {
		add_channel,
		channel_name,
		channel_name_placeholder,
		channel_type,
		select_channel_description,
		error_select_channel_type
	} from '$lib/paraglide/messages';

	const dispatch = createEventDispatcher<{
		submit: { type: TransporterType; config: any };
	}>();

	let selectedType: TransporterType = TransporterType.WHATSAPP;
	let channelName = '';
	let error: string | null = null;

	function handleSubmit(e: Event) {
		e.preventDefault();
		if (!selectedType) {
			error = error_select_channel_type();
			return;
		}

		const config: any = {};

		if (channelName) {
			config.name = channelName;
		}

		dispatch('submit', {
			type: selectedType,
			config
		});
	}
</script>

<form id="add-channel-form" onsubmit={handleSubmit}>
	<div class="grid gap-4 py-4">
		{#if error}
			<div class="text-sm text-red-500">{error}</div>
		{/if}

		<div class="grid gap-2">
			<Label for="channel-type">{channel_type()}</Label>
			<RadioGroup bind:value={selectedType}>
				{#each Object.values(CHANNEL_INFO) as channel}
					<div class="flex items-center space-x-2">
						<RadioGroupItem value={channel.type} id={channel.type} />
						<Label for={channel.type} class="cursor-pointer">
							<div class="flex items-center gap-2">
								<ChannelIcon type={channel.type} size="sm" />
								<span>{channel.title}</span>
							</div>
						</Label>
					</div>
				{/each}
			</RadioGroup>
		</div>

		<div class="grid gap-2">
			<Label for="channel-name">{channel_name()} ({select_channel_description()})</Label>
			<Input id="channel-name" bind:value={channelName} placeholder={channel_name_placeholder()} />
		</div>
	</div>
</form>
