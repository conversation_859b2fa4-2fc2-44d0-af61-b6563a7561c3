<script lang="ts">
	import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from '$lib/components/ui/card';
	import type { TransporterType } from '$lib/types/channel.types';
	import { CHANNEL_INFO } from '$lib/utils/channel';
	import ChannelIcon from '$lib/components/channel/ChannelIcon.svelte';
	import AddIcon from '~icons/material-symbols/add';
	import { coming_soon } from '$lib/paraglide/messages';
	import LoadingIcon from '~icons/material-symbols/refresh';

	export let onSelect: (type: TransporterType) => void;
	export let isChannelProcessing = false;
</script>

<div class="grid gap-4">
	{#each Object.values(CHANNEL_INFO) as channel, i}
		<Card
			class="hover:bg-muted/50 cursor-pointer transition-all duration-200 hover:scale-[1.01] hover:shadow-sm slide-up fade-in {channel.isComingSoon
				? 'opacity-80'
				: ''}"
			style="--animation-delay: {i * 100}ms;"
			onclick={() => !isChannelProcessing && onSelect(channel.type)}
		>
			<CardHeader>
				<div class="flex items-center gap-2">
					<CardTitle class="font-light">{channel.title}</CardTitle>
				</div>
			</CardHeader>
			<CardContent class="flex items-center gap-4 p-4">
				<div class="bg-primary/10 rounded-md p-2">
					<ChannelIcon type={channel.type} size="md" />
				</div>
				<div>
					<p class="text-muted-foreground text-sm">{channel.description}</p>
				</div>
				{#if channel.isComingSoon}
					<span class="text-muted-foreground ml-auto text-xs">{coming_soon()}</span>
				{:else if isChannelProcessing}
					<LoadingIcon class="text-muted-foreground ml-auto h-8 w-8 animate-spin" />
				{:else}
					<AddIcon class="text-muted-foreground ml-auto h-8 w-8" />
				{/if}
			</CardContent>
		</Card>
	{/each}
</div>
