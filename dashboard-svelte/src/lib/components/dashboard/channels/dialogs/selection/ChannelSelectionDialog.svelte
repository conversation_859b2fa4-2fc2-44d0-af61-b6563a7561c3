<script lang="ts">
	import * as Dialog from '$lib/components/ui/dialog';
	import * as Drawer from '$lib/components/ui/drawer';
	import { Button, buttonVariants } from '$lib/components/ui/button';
	import type { TransporterType } from '$lib/types/channel.types';
	import ChannelSelectionForm from './ChannelSelectionForm.svelte';
	import { useResponsive } from '$lib/utils/responsive';
	import { select_channel, select_channel_description, close } from '$lib/paraglide/messages';

	export let open = false;
	export let onSelect: (type: TransporterType) => void;
	export let isChannelProcessing = false;

	const { isMobile } = useResponsive();
	$: isDesktop = !$isMobile;
</script>

{#if isDesktop}
	<Dialog.Root bind:open>
		<Dialog.Content class="sm:max-w-[425px]">
			<Dialog.Header>
				<Dialog.Title class="font-medium lowercase">{select_channel()}</Dialog.Title>
				<Dialog.Description class="lowercase">
					{select_channel_description()}
				</Dialog.Description>
			</Dialog.Header>
			<div class="py-4">
				<ChannelSelectionForm {onSelect} {isChannelProcessing} />
			</div>
			<Dialog.Footer>
				<Button variant="outline" onclick={() => (open = false)}>{close()}</Button>
			</Dialog.Footer>
		</Dialog.Content>
	</Dialog.Root>
{:else}
	<Drawer.Root bind:open>
		<Drawer.Content>
			<Drawer.Header class="text-left">
				<Drawer.Title class="font-medium lowercase">{select_channel()}</Drawer.Title>
				<Drawer.Description class="lowercase">
					{select_channel_description()}
				</Drawer.Description>
			</Drawer.Header>
			<div class="px-4">
				<ChannelSelectionForm {onSelect} {isChannelProcessing} />
			</div>
			<Drawer.Footer class="pt-2">
				<Drawer.Close class={buttonVariants({ variant: 'outline' })}>{close()}</Drawer.Close>
			</Drawer.Footer>
		</Drawer.Content>
	</Drawer.Root>
{/if}
