<script lang="ts">
	import { Button } from '$lib/components/ui/button';
	import Textarea from '$lib/components/ui/textarea/textarea.svelte';
	import { toast } from 'svelte-sonner';
	import { History as HistoryIcon, DotIcon, GitBranch as DiffIcon } from '@lucide/svelte';
	import { Switch } from '$lib/components/ui/switch';
	import { browser } from '$app/environment';
	import HistoryDialog from './HistoryDialog.svelte';
	import DiffView from './DiffView.svelte';
	import { SERVICE_NAME } from '$lib/constants/telemetry';
	import * as logsAPI from '@opentelemetry/api-logs';
	import PromptHistoryForm from './PromptHistoryForm.svelte';
	import { history_title, history_description, symbols, tokens } from '$lib/paraglide/messages';
	import type { PromptType } from '$lib/types/prompt.types';
	import { onMount } from 'svelte';
	import { Skeleton } from '$lib/components/ui/skeleton';
	import { history } from '$lib/paraglide/messages';
	import type { AbstractPromptStore } from '$lib/stores/abstract-prompt-store';

	const logger = logsAPI.logs.getLogger(SERVICE_NAME);

	export let type: PromptType;
	export let label: string;
	export let placeholder: string;
	export let maxLength: number;
	export let successMessage: string;
	export let formError: string | undefined;
	export let onClose: () => void;
	export let store: AbstractPromptStore;

	let showHistory = false;
	let showDiff = false;
	let showGitPatch = false;
	let originalContent = '';
	let content = '';
	let isLoading = true;
	let isSaving = false;
	let promptTokens = 0;
	let originalTokens = 0;
	let isTokenLoading = false;
	let isOriginalTokenLoading = false;
	let gitPatch: string | null = null;
	let highlightedDiff = '';

	$: {
		content = $store[type]?.content ?? '';
		isLoading = $store[type]?.isLoading ?? true;
		isSaving = $store[type]?.isSaving ?? false;
		promptTokens = $store[type]?.tokenCount ?? 0;
		isTokenLoading = $store[type]?.areTokensLoading ?? false;

		// Set original content when loading is complete and we haven't set it yet
		if (!isLoading && !originalContent && content) {
			originalContent = content;
			originalTokens = promptTokens;
		}
	}

	function updateValue(e: Event) {
		const target = e.target as HTMLTextAreaElement;
		store.updateContent(type, target.value);
	}

	function handleRestore(content: string) {
		store.updateContent(type, content);
		toast.success('Версия восстановлена');
	}

	function handleCancel() {
		store.loadPrompt(type);
		onClose();
	}

	onMount(() => {
		store.loadPrompt(type);
	});

	async function generatePatch() {
		try {
			const res = await fetch('/api/diff', {
				method: 'POST',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify({ left: originalContent ?? '', right: content ?? '' })
			});
			if (res.ok) {
				const data = await res.json();
				gitPatch = (data?.diff ?? '').trim();
			} else {
				gitPatch = '';
			}
		} catch {
			gitPatch = '';
		}
	}

	$: if (showDiff && showGitPatch && !isLoading) {
		generatePatch();
	}

	$: highlightedDiff = browser && gitPatch ? gitPatch : '';

	async function handleSubmit(event: Event) {
		event.preventDefault();
		if (isSaving) return;

		try {
			await store.savePrompt(type, successMessage);
		} catch (error) {
			logger.emit({
				body: `Failed to save prompt: ${error instanceof Error ? error.message : String(error)}`,
				severityNumber: logsAPI.SeverityNumber.ERROR,
				severityText: 'ERROR'
			});
		}
	}
</script>

<form onsubmit={handleSubmit} class="flex h-full flex-col">
	<div class="flex flex-1 flex-col">
		<div class="flex h-full flex-1 flex-col">
			<div class="mb-2">
				<label for="textarea-{type}" class="block text-sm font-medium">{label}</label>
			</div>
			<div class="relative h-full flex-1">
				{#if showDiff}
					<DiffView
						enableDiff={showGitPatch}
						showDiff={showGitPatch}
						{gitPatch}
						{highlightedDiff}
						leftContent={originalContent}
						rightContent={content}
						{isLoading}
						isLeftTokenLoading={isOriginalTokenLoading}
						isRightTokenLoading={isTokenLoading}
						leftTokens={originalTokens}
						rightTokens={promptTokens}
						maxlength={maxLength}
						leftLabel="original version"
						rightLabel="current version"
					/>
				{:else if isLoading}
					<div class="absolute inset-0">
						<Skeleton class="h-full w-full" />
					</div>
				{:else}
					<Textarea
						id="textarea-{type}"
						name={type}
						value={content}
						oninput={updateValue}
						{placeholder}
						maxlength={maxLength}
						countLabel={symbols()}
						showCount={true}
						autoGrow={false}
						minRows={30}
						error={formError}
						class="absolute inset-0 h-full resize-none"
						style="min-height: calc(100vh - 250px);"
					>
						<span slot="alongside-count" class="text-sm text-gray-400">
							{#if isTokenLoading}
								<Skeleton class="h-4 w-16" />
							{:else}
								<div class="flex items-center gap-1">
									<DotIcon class="h-4 w-4" />
									<span>{promptTokens} {tokens()}</span>
								</div>
							{/if}
						</span>
					</Textarea>
				{/if}
			</div>
		</div>
	</div>
	{#if formError}
		<p class="mt-2 text-sm text-red-500">{formError}</p>
	{/if}
	<div class="mt-4 flex justify-between gap-2">
		<div class="flex items-center gap-3">
			<Button
				type="button"
				variant="ghost"
				size="sm"
				class="flex items-center gap-2"
				onclick={() => (showHistory = true)}
				disabled={isLoading}
			>
				<HistoryIcon class="h-4 w-4" />
				<span>{history()}</span>
			</Button>
			<Button
				type="button"
				variant={showDiff ? 'default' : 'ghost'}
				size="sm"
				class="flex items-center gap-2"
				onclick={() => (showDiff = !showDiff)}
				disabled={isLoading}
			>
				<DiffIcon class="h-4 w-4" />
				<span>diff</span>
			</Button>
			{#if showDiff}
				<div class="flex items-center gap-2">
					<Switch id="git-patch-mode" bind:checked={showGitPatch} />
					<label for="git-patch-mode" class="text-muted-foreground text-sm">git patch</label>
				</div>
			{/if}
		</div>
		<div class="flex gap-2">
			<Button
				type="button"
				variant="outline"
				onclick={handleCancel}
				disabled={isSaving || isLoading}>отменa</Button
			>
			<Button type="submit" disabled={isSaving || isLoading}>
				{isSaving ? 'загрузка...' : 'сохранить'}
			</Button>
		</div>
	</div>
</form>

<HistoryDialog bind:open={showHistory} title={history_title()} description={history_description()}>
	<PromptHistoryForm
		{type}
		onRestore={handleRestore}
		maxlength={maxLength}
		onClose={() => (showHistory = false)}
		{store}
	/>
</HistoryDialog>
