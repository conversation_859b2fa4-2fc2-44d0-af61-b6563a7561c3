<script lang="ts">
	import type { KnowledgeType } from '$lib/types/knowledge.types';
	import KnowledgeDialog from './KnowledgeDialog.svelte';
	import { Button } from '$lib/components/ui/button';
	import { onai_knowledge } from '$lib/paraglide/messages';

	let showKnowledgeDialog = false;
	let selectedKnowledgeType: KnowledgeType = 'text';

	function showKnowledge() {
		showKnowledgeDialog = true;
	}
</script>

<div class="w-full">
	<div class="flex flex-wrap gap-2">
		<Button variant="default" onclick={showKnowledge}>
			{onai_knowledge()}
		</Button>
	</div>
</div>

{#if showKnowledgeDialog}
	<KnowledgeDialog
		bind:open={showKnowledgeDialog}
		bind:type={selectedKnowledgeType}
		title={onai_knowledge()}
	/>
{/if}
