<script lang="ts">
	import { Button } from '$lib/components/ui/button';
	import type { KnowledgeType } from '$lib/types/knowledge.types';
	import {
		history,
		cancel,
		knowledge_saving,
		knowledge_updating,
		knowledge_uploading
	} from '$lib/paraglide/messages';

	export let type: KnowledgeType;
	export let showHistory: boolean;
	export let canSave: boolean;
	export let isSaving: boolean;
	export let knowledgeExists: boolean;
	export let onHistory: () => void;
	export let onCancel: () => void;
	export let onSave: () => void;

	$: saveButtonText = isSaving
		? knowledge_saving()
		: knowledgeExists
			? knowledge_updating()
			: knowledge_uploading();
</script>

<div class="mt-4 flex gap-2 border-t pt-4">
	<div class="flex-grow">
		{#if showHistory}
			<Button
				variant="outline"
				onclick={onHistory}
				data-ph-capture-attribute-knowledge-type={type}
				data-ph-capture-attribute-action="knowledge-history"
				data-ph-capture-attribute-knowledge-exists={knowledgeExists}
			>
				{history()}
			</Button>
		{/if}
	</div>
	<Button
		onclick={onSave}
		disabled={!canSave}
		data-ph-capture-attribute-knowledge-type={type}
		data-ph-capture-attribute-action={knowledgeExists ? 'knowledge-update' : 'knowledge-create'}
		data-ph-capture-attribute-can-save={canSave}
		data-ph-capture-attribute-is-saving={isSaving}
	>
		{saveButtonText}
	</Button>
</div>
