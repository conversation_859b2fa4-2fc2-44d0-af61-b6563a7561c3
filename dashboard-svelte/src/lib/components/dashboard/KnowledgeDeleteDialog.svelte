<script lang="ts">
	import * as Dialog from '$lib/components/ui/dialog';
	import * as Drawer from '$lib/components/ui/drawer';
	import { useResponsive } from '$lib/utils/responsive';
	import { knowledge_delete } from '$lib/paraglide/messages';
	import KnowledgeDeleteForm from './KnowledgeDeleteForm.svelte';

	export let open = false;
	export let knowledgeId: string | null = null;
	export let type: 'text' | 'document' = 'text';
	export let onDeleted: () => void = () => {};

	const { isMobile } = useResponsive();
	$: isDesktop = !$isMobile;
</script>

{#if isDesktop}
	<Dialog.Root bind:open>
		<Dialog.Content>
			<Dialog.Header>
				<Dialog.Title>{knowledge_delete()}</Dialog.Title>
			</Dialog.Header>
			<KnowledgeDeleteForm {knowledgeId} {type} onClose={() => (open = false)} {onDeleted} />
		</Dialog.Content>
	</Dialog.Root>
{:else}
	<Drawer.Root bind:open>
		<Drawer.Content>
			<Drawer.Header class="text-left">
				<Drawer.Title>{knowledge_delete()}</Drawer.Title>
			</Drawer.Header>
			<div class="px-4">
				<KnowledgeDeleteForm {knowledgeId} {type} onClose={() => (open = false)} {onDeleted} />
			</div>
		</Drawer.Content>
	</Drawer.Root>
{/if}
