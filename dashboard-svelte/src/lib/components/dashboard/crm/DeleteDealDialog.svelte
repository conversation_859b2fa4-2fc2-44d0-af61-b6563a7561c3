<script lang="ts">
	import * as Dialog from '$lib/components/ui/dialog';
	import * as Drawer from '$lib/components/ui/drawer';
	import { useResponsive } from '$lib/utils/responsive';
	import DeleteDealForm from './DeleteDealForm.svelte';

	export let open = false;
	export let dealId: string;

	const { isMobile } = useResponsive();
	$: isDesktop = !$isMobile;
</script>

{#if isDesktop}
	<Dialog.Root bind:open>
		<Dialog.Content>
			<Dialog.Header>
				<Dialog.Title>delete deal</Dialog.Title>
			</Dialog.Header>
			<DeleteDealForm {dealId} onClose={() => (open = false)} />
		</Dialog.Content>
	</Dialog.Root>
{:else}
	<Drawer.Root bind:open>
		<Drawer.Content>
			<Drawer.Header class="text-left">
				<Drawer.Title>delete deal</Drawer.Title>
			</Drawer.Header>
			<div class="px-4">
				<DeleteDealForm {dealId} onClose={() => (open = false)} />
			</div>
		</Drawer.Content>
	</Drawer.Root>
{/if}
