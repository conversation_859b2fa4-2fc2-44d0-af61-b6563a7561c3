<script lang="ts">
	import { funnelStore } from '$lib/stores/funnel-store';
	import { Button } from '$lib/components/ui/button';
	import {
		DropdownMenu,
		DropdownMenuContent,
		DropdownMenuItem,
		DropdownMenuLabel,
		DropdownMenuSeparator,
		DropdownMenuTrigger
	} from '$lib/components/ui/dropdown-menu';
	import { PlusIcon, FilterIcon, CheckIcon, PencilIcon, TrashIcon } from '@lucide/svelte';
	import FunnelDialog from './FunnelDialog.svelte';
	import FunnelDeleteDialog from './FunnelDeleteDialog.svelte';
	import {
		funnel_select,
		funnel_loading,
		no_funnels_available,
		funnel_new,
		funnel_edit
	} from '$lib/paraglide/messages';
	import { analytics } from '$lib/services/analytics';

	let newFunnelDialogOpen = false;
	let editFunnelDialogOpen = false;
	let editingFunnelId: string | undefined = undefined;
	let deleteFunnelDialogOpen = false;
	let deletingFunnelId: string | undefined = undefined;
	let deletingFunnelName: string = '';

	function openNewFunnelDialog() {
		analytics.trackButtonClicked('new_funnel', 'funnel_selector', 'primary');
		analytics.trackModalOpened('funnel_dialog', 'creation');
		newFunnelDialogOpen = true;
	}

	function openEditFunnelDialog(funnelId: string, event: MouseEvent) {
		event.stopPropagation();

		analytics.trackButtonClicked('edit_funnel', 'funnel_selector', 'outline');
		analytics.trackModalOpened('funnel_dialog', 'edit');
		editingFunnelId = funnelId;
		editFunnelDialogOpen = true;
	}

	function openDeleteFunnelDialog(funnelId: string, funnelName: string, event: MouseEvent) {
		event.stopPropagation();

		analytics.trackButtonClicked('delete_funnel', 'funnel_selector', 'destructive');
		analytics.trackModalOpened('funnel_delete_dialog', 'deletion');
		deletingFunnelId = funnelId;
		deletingFunnelName = funnelName;
		deleteFunnelDialogOpen = true;
	}

	function setActiveFunnel(funnelId: string) {
		analytics.trackDropdownUsed('funnel_selector', funnelId, 'crm_tab');
		analytics.trackCrmFeatureUsed('funnel_selected', funnelId);
		funnelStore.setActiveFunnel(funnelId);
	}

	$: activeFunnel = $funnelStore.funnels.find((f) => f.id === $funnelStore.activeFunnelId);
</script>

<div class="flex items-center space-x-2">
	<DropdownMenu>
		<DropdownMenuTrigger>
			<button
				class="flex items-center gap-1 rounded-md bg-slate-100 px-3 py-2 text-xs font-medium text-slate-700 hover:bg-slate-200 dark:bg-slate-800 dark:text-slate-300 dark:hover:bg-slate-700"
			>
				<FilterIcon class="h-4 w-4" />
				<span class="hidden md:inline">
					{activeFunnel?.name || funnel_select()}
				</span>
			</button>
		</DropdownMenuTrigger>
		<DropdownMenuContent align="end" class="w-56">
			{#if $funnelStore.loading}
				<DropdownMenuItem disabled>{funnel_loading()}</DropdownMenuItem>
			{:else if $funnelStore.funnels.length === 0}
				<DropdownMenuItem disabled>{no_funnels_available()}</DropdownMenuItem>
			{:else}
				{#each $funnelStore.funnels as funnel (funnel.id)}
					<DropdownMenuItem
						onclick={() => setActiveFunnel(funnel.id)}
						class="flex items-center justify-between"
					>
						<div class="flex items-center gap-2">
							<span>{funnel.name}</span>
							{#if funnel.id === $funnelStore.activeFunnelId}
								<CheckIcon class="text-primary h-4 w-4" />
							{/if}
						</div>
						<div class="flex items-center gap-2">
							<button
								class="text-muted-foreground hover:text-foreground"
								onclick={(e) => openEditFunnelDialog(funnel.id, e)}
							>
								<PencilIcon class="h-4 w-4" />
							</button>
							{#if !funnel.is_default}
								<button
									class="text-muted-foreground hover:text-destructive"
									onclick={(e) => openDeleteFunnelDialog(funnel.id, funnel.name, e)}
								>
									<TrashIcon class="h-4 w-4" />
								</button>
							{/if}
						</div>
					</DropdownMenuItem>
				{/each}
			{/if}
			<DropdownMenuSeparator />
			<DropdownMenuItem onclick={openNewFunnelDialog} class="flex items-center gap-2">
				<PlusIcon class="h-4 w-4" />
				<span>{funnel_new()}</span>
			</DropdownMenuItem>
		</DropdownMenuContent>
	</DropdownMenu>
</div>

<FunnelDialog bind:open={newFunnelDialogOpen} />
<FunnelDialog bind:open={editFunnelDialogOpen} editMode={true} funnelId={editingFunnelId} />
<FunnelDeleteDialog
	bind:open={deleteFunnelDialogOpen}
	funnelId={deletingFunnelId}
	funnelName={deletingFunnelName}
/>
