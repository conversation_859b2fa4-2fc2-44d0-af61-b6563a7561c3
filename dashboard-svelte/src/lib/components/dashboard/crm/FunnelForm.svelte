<script lang="ts">
	import { funnelStore } from '$lib/stores/funnel-store';
	import { Button } from '$lib/components/ui/button';
	import { Input } from '$lib/components/ui/input';
	import { Label } from '$lib/components/ui/label';
	import { Textarea } from '$lib/components/ui/textarea';
	import { Checkbox } from '$lib/components/ui/checkbox';
	import {
		funnel_name,
		funnel_name_placeholder,
		funnel_description,
		funnel_description_placeholder,
		funnel_set_default,
		cancel,
		funnel_create_button,
		funnel_update_button,
		loading,
		funnel_create_success,
		funnel_update_success,
		funnel_save_error
	} from '$lib/paraglide/messages';
	import { toast } from 'svelte-sonner';
	import type { CrmFunnelUpdate } from '$lib/types/crm.types';

	export let onClose: () => void;
	export let editMode = false;
	export let funnelId: string | undefined = undefined;

	// Form state
	let funnelName = '';
	let funnelDescription = '';
	let funnelIsDefault = false;
	let isSubmitting = false;

	// If in edit mode and funnelId is provided, load the funnel data
	$: if (editMode && funnelId) {
		const funnel = $funnelStore.funnels.find((f) => f.id === funnelId);
		if (funnel) {
			funnelName = funnel.name;
			funnelDescription = funnel.description || '';
			funnelIsDefault = funnel.is_default || false;
		}
	}

	async function handleSubmit() {
		if (!funnelName.trim()) return;

		isSubmitting = true;
		try {
			if (editMode && funnelId) {
				const updateData: CrmFunnelUpdate = {
					name: funnelName.trim(),
					description: funnelDescription.trim() || null,
					is_default: funnelIsDefault
				};
				await funnelStore.updateFunnel(funnelId, updateData);
				toast.success(funnel_update_success());
			} else {
				await funnelStore.createFunnel(
					funnelName.trim(),
					funnelDescription.trim() || undefined,
					funnelIsDefault
				);
				toast.success(funnel_create_success());
			}
			onClose();
		} catch (error) {
			console.error('Failed to save funnel:', error);
			toast.error(funnel_save_error());
		} finally {
			isSubmitting = false;
		}
	}
</script>

<div class="grid gap-4 py-4">
	<div class="grid gap-2">
		<Label for="name">{funnel_name()}</Label>
		<Input id="name" bind:value={funnelName} placeholder={funnel_name_placeholder()} />
	</div>
	<div class="grid gap-2">
		<Label for="description">{funnel_description()}</Label>
		<Textarea
			id="description"
			bind:value={funnelDescription}
			placeholder={funnel_description_placeholder()}
		/>
	</div>
	<div class="flex items-center space-x-2">
		<Checkbox id="is-default" bind:checked={funnelIsDefault} />
		<Label for="is-default">{funnel_set_default()}</Label>
	</div>
</div>
<div class="flex justify-end gap-3 pt-4">
	<Button variant="outline" onclick={onClose} disabled={isSubmitting}>
		{cancel()}
	</Button>
	<Button onclick={handleSubmit} disabled={!funnelName.trim() || isSubmitting}>
		{isSubmitting ? loading() : editMode ? funnel_update_button() : funnel_create_button()}
	</Button>
</div>
