<script lang="ts">
	import { Button } from '$lib/components/ui/button';
	import { funnelStore } from '$lib/stores/funnel-store';
	import { toast } from 'svelte-sonner';
	import type { UUID } from '$lib/types/on-ai.types';
	import {
		cancel,
		funnel_delete_confirm,
		funnel_delete_button,
		funnel_deleting,
		funnel_delete_success
	} from '$lib/paraglide/messages';

	export let onClose: () => void;
	export let funnelId: UUID | undefined = undefined;
	export let funnelName: string = '';

	let isDeleting = false;

	async function handleDelete() {
		if (!funnelId) return;

		try {
			isDeleting = true;
			await funnelStore.deleteFunnel(funnelId);
			toast.success(funnel_delete_success({ funnelName }));
			onClose();
		} catch (error) {
			console.error('Failed to delete funnel:', error);
			const errorMessage = error instanceof Error ? error.message : 'Failed to delete funnel';
			toast.error(errorMessage);
		} finally {
			isDeleting = false;
		}
	}
</script>

<div>
	<p class="text-muted-foreground text-sm">
		{funnel_delete_confirm({ funnelName })}
	</p>
	<div class="flex justify-end gap-3 pt-4">
		<Button variant="outline" onclick={onClose} disabled={isDeleting}>{cancel()}</Button>
		<Button variant="destructive" onclick={handleDelete} disabled={isDeleting}>
			{isDeleting ? funnel_deleting() : funnel_delete_button()}
		</Button>
	</div>
</div>
