<script lang="ts">
	import * as Dialog from '$lib/components/ui/dialog';
	import * as Drawer from '$lib/components/ui/drawer';
	import { useResponsive } from '$lib/utils/responsive';
	import FunnelDeleteForm from './FunnelDeleteForm.svelte';
	import type { UUID } from '$lib/types/on-ai.types';

	export let open = false;
	export let funnelId: UUID | undefined = undefined;
	export let funnelName: string = '';

	const { isMobile } = useResponsive();
	$: isDesktop = !$isMobile;
</script>

{#if isDesktop}
	<Dialog.Root bind:open>
		<Dialog.Content>
			<Dialog.Header>
				<Dialog.Title>delete funnel</Dialog.Title>
			</Dialog.Header>
			<FunnelDeleteForm {funnelId} {funnelName} onClose={() => (open = false)} />
		</Dialog.Content>
	</Dialog.Root>
{:else}
	<Drawer.Root bind:open>
		<Drawer.Content>
			<Drawer.Header class="text-left">
				<Drawer.Title>delete funnel</Drawer.Title>
			</Drawer.Header>
			<div class="px-4">
				<FunnelDeleteForm {funnelId} {funnelName} onClose={() => (open = false)} />
			</div>
		</Drawer.Content>
	</Drawer.Root>
{/if}


