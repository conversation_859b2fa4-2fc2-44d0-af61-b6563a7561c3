<script lang="ts">
	import { Button } from '$lib/components/ui/button';
	import { crmStore } from '$lib/stores/crm-store';
	import { toast } from 'svelte-sonner';
	import { cancel } from '$lib/paraglide/messages';

	export let dealId: string;
	export let onClose: () => void;

	let isDeleting = false;

	async function handleDelete() {
		isDeleting = true;
		try {
			await crmStore.deleteDeal(dealId);
			toast.success('deal deleted');
			onClose();
		} catch (error) {
			toast.error('failed to delete deal');
		} finally {
			isDeleting = false;
		}
	}
</script>

<div>
	<p class="text-muted-foreground text-sm">are you sure you want to delete this deal?</p>
	<div class="flex justify-end gap-3 pt-4">
		<Button variant="outline" onclick={onClose} disabled={isDeleting}>{cancel()}</Button>
		<Button variant="destructive" onclick={handleDelete} disabled={isDeleting}>
			{isDeleting ? 'deleting...' : 'delete'}
		</Button>
	</div>
</div>
