<script lang="ts">
	import * as Dialog from '$lib/components/ui/dialog';
	import * as Drawer from '$lib/components/ui/drawer';
	import { useResponsive } from '$lib/utils/responsive';
	import FunnelForm from './FunnelForm.svelte';
	import { funnel_create, funnel_edit } from '$lib/paraglide/messages';

	export let open = false;
	export let editMode = false;
	export let funnelId: string | undefined = undefined;
	export let title = editMode ? funnel_edit() : funnel_create();

	const { isMobile } = useResponsive();
	$: isDesktop = !$isMobile;
</script>

{#if isDesktop}
	<Dialog.Root bind:open>
		<Dialog.Content>
			<Dialog.Header>
				<Dialog.Title>{title}</Dialog.Title>
			</Dialog.Header>
			<FunnelForm onClose={() => (open = false)} {editMode} {funnelId} />
		</Dialog.Content>
	</Dialog.Root>
{:else}
	<Drawer.Root bind:open>
		<Drawer.Content>
			<Drawer.Header class="text-left">
				<Drawer.Title>{title}</Drawer.Title>
			</Drawer.Header>
			<div class="px-4">
				<FunnelForm onClose={() => (open = false)} {editMode} {funnelId} />
			</div>
		</Drawer.Content>
	</Drawer.Root>
{/if}
