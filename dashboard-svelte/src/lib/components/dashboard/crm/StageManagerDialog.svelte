<script lang="ts">
	import * as Dialog from '$lib/components/ui/dialog';
	import * as Drawer from '$lib/components/ui/drawer';
	import { createEventDispatcher, onMount } from 'svelte';
	import { browser } from '$app/environment';
	import StageManagerForm from './StageManagerForm.svelte';
	import { kanbanStageStore } from '$lib/stores/kanban-stage-store';
	import type { CrmStage } from '$lib/types/crm.types';
	import { useResponsive } from '$lib/utils/responsive';
	import { v7 as uuidv7 } from 'uuid';
	import { getRandomColor } from '$lib/utils/random';
	import {
		stage_manager_title,
		stage_manager_description,
		new_stage,
		select_funnel_first
	} from '$lib/paraglide/messages';
	import { funnelStore } from '$lib/stores/funnel-store';
	import { toast } from 'svelte-sonner';
	import { CrmStageType } from '$lib/enums/crm.enums';

	let workingStages: CrmStage[] = [];
	let isDialogOpen: boolean;

	kanbanStageStore.dialogOpen.subscribe((value) => {
		isDialogOpen = value;
		if (value) {
			updateWorkingStages();
		}
	});

	const { isMobile } = useResponsive();
	$: isDesktop = !$isMobile;

	const dispatch = createEventDispatcher<{
		saveStages: { stages: CrmStage[] };
	}>();

	function addStage() {
		const activeFunnelId = $funnelStore.activeFunnelId;
		if (!activeFunnelId) {
			toast.error(select_funnel_first());
			return;
		}

		workingStages = [
			...workingStages,
			{
				id: uuidv7(),
				name: new_stage(),
				color: getRandomColor(),
				funnel_id: activeFunnelId,
				order: workingStages.length,
				type: CrmStageType.NORMAL
			}
		];
	}

	function removeStage(index: number) {
		workingStages = workingStages.filter((_, i) => i !== index);
	}

	function saveStages() {
		dispatch('saveStages', { stages: workingStages });
		kanbanStageStore.closeStageManager();
	}

	function cancel() {
		updateWorkingStages();
		kanbanStageStore.closeStageManager();
	}

	function updateWorkingStages() {
		workingStages = Array.isArray($kanbanStageStore) ? [...$kanbanStageStore] : [];
	}
</script>

{#if isDesktop}
	<Dialog.Root
		bind:open={isDialogOpen}
		onOpenChange={(open) => kanbanStageStore.dialogOpen.set(open)}
	>
		<Dialog.Portal>
			<Dialog.Overlay />
			<Dialog.Content class="sm:max-w-md">
				<Dialog.Header>
					<Dialog.Title>{stage_manager_title()}</Dialog.Title>
					<Dialog.Description>
						{stage_manager_description()}
					</Dialog.Description>
				</Dialog.Header>

				<StageManagerForm bind:workingStages {addStage} {removeStage} {saveStages} {cancel} />
			</Dialog.Content>
		</Dialog.Portal>
	</Dialog.Root>
{:else}
	<Drawer.Root
		bind:open={isDialogOpen}
		onOpenChange={(open) => kanbanStageStore.dialogOpen.set(open)}
	>
		<Drawer.Content>
			<Drawer.Header class="text-left">
				<Drawer.Title>{stage_manager_title()}</Drawer.Title>
				<Drawer.Description>
					{stage_manager_description()}
				</Drawer.Description>
			</Drawer.Header>

			<StageManagerForm
				bind:workingStages
				{addStage}
				{removeStage}
				{saveStages}
				{cancel}
				isMobile={true}
			/>
		</Drawer.Content>
	</Drawer.Root>
{/if}
