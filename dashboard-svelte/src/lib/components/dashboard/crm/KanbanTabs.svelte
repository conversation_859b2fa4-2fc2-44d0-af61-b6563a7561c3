<script lang="ts">
	import * as Tabs from '$lib/components/ui/tabs';
	import { Card, CardContent, CardHeader, CardTitle } from '$lib/components/ui/card';
	import { buttonVariants } from '$lib/components/ui/button';
	import { cn } from '$lib/utils';
	import { createEventDispatcher } from 'svelte';
	import { unnamed } from '$lib/paraglide/messages';
	import { formatDate } from '$lib/utils/date';
	import { kanbanStageStore } from '$lib/stores/kanban-stage-store';
	import type { CrmStage } from '$lib/types/crm.types';
	import { crmStore } from '$lib/stores/crm-store';
	import type { CrmDeal } from '$lib/types/crm.types';
	import { Trash2 } from '@lucide/svelte';

	const dispatch = createEventDispatcher<{
		moveApi: { apiId: string; fromStatus: string; toStatus: string };
		addDeal: { name: string; stageId: string };
		deleteDeal: { dealId: string };
	}>();

	$: stagesArray = Array.isArray($kanbanStageStore) ? $kanbanStageStore : [];
	$: validStages = stagesArray.filter(
		(stage): stage is CrmStage & { id: string } => stage && typeof stage.id === 'string'
	);
	$: statuses = validStages.map((stage) => stage.id);

	$: dealsByStatus = statuses.reduce(
		(acc, currentStatus) => {
			acc[currentStatus] = $crmStore.deals.filter((deal) => deal.stage_id === currentStatus);
			return acc;
		},
		{} as Record<string, CrmDeal[]>
	);

	let activeTab: string;

	$: {
		const newDefaultTab = statuses.length > 0 ? statuses[0] : 'backlog';
		if (activeTab === undefined && newDefaultTab) {
			activeTab = newDefaultTab;
		} else if (statuses.length > 0 && !statuses.includes(activeTab)) {
			activeTab = newDefaultTab;
		} else if (statuses.length === 0 && activeTab !== 'backlog') {
			activeTab = 'backlog';
		}
	}

	function getDescription(deal: CrmDeal): string {
		return `created: ${formatDate(new Date(deal.created_at))}`;
	}
</script>

<Tabs.Root bind:value={activeTab} class="w-full">
	<div class="border-b bg-white px-4 dark:border-slate-700 dark:bg-slate-900">
		<Tabs.List class="flex items-center overflow-x-auto py-2">
			{#each validStages as stage}
				<Tabs.Trigger
					value={stage.id}
					class="flex items-center gap-1.5 whitespace-nowrap px-3 py-1.5"
				>
					<span class="h-2 w-2 rounded-full" style="background-color: {stage.color};"></span>
					<span>{stage.name}</span>
					<span
						class="ml-1 flex h-5 w-5 items-center justify-center rounded-full bg-zinc-100 text-xs dark:bg-zinc-800"
					>
						{dealsByStatus[stage.id]?.length || 0}
					</span>
				</Tabs.Trigger>
			{/each}
		</Tabs.List>
	</div>

	{#each validStages as stage}
		<Tabs.Content value={stage.id} class="p-4">
			<div class="grid gap-4">
				{#if $crmStore.loading}
					{#each Array(3) as _}
						<Card>
							<CardHeader>
								<CardTitle>
									<div class="h-5 w-3/4 animate-pulse rounded bg-slate-200 dark:bg-slate-700"></div>
								</CardTitle>
							</CardHeader>
							<CardContent>
								<div class="space-y-3">
									<div class="h-4 w-1/2 animate-pulse rounded bg-slate-200 dark:bg-slate-700"></div>
									<div class="h-4 w-1/3 animate-pulse rounded bg-slate-200 dark:bg-slate-700"></div>
								</div>
							</CardContent>
						</Card>
					{/each}
				{:else if dealsByStatus[stage.id]?.length === 0}
					<div class="p-6 text-center text-sm text-slate-500 dark:text-slate-400">
						no deals in this status
					</div>
				{:else}
					{#each dealsByStatus[stage.id] || [] as deal}
						<Card>
							<CardHeader>
								<CardTitle>
									<div class="flex items-center justify-between">
										<span class="font-medium">{deal.name || unnamed()}</span>
										<div class="flex items-center">
											<span class="text-muted-foreground shrink-0 text-xs"
												>{formatDate(new Date(deal.created_at))}</span
											>
											<button
												type="button"
												class={cn(buttonVariants({ variant: 'ghost', size: 'icon' }), 'h-6 w-6')}
												onclick={() => dispatch('deleteDeal', { dealId: deal.id })}
											>
												<Trash2 class="text-destructive h-4 w-4" />
											</button>
										</div>
									</div>
								</CardTitle>
							</CardHeader>
							<CardContent>
								<div class="mb-4">
									<p class="text-sm text-slate-600 dark:text-slate-400">
										{getDescription(deal)}
									</p>
								</div>

								<div class="flex flex-wrap gap-2">
									{#each statuses.filter((targetStatus) => targetStatus !== stage.id) as targetStatus}
										<button
											type="button"
											class={cn(
												buttonVariants({ size: 'sm', variant: 'outline' }),
												'h-8 rounded-full text-xs'
											)}
											onclick={() => {
												dispatch('moveApi', {
													apiId: deal.id,
													fromStatus: stage.id,
													toStatus: targetStatus
												});
											}}
										>
											{validStages.find((s) => s.id === targetStatus)?.name || targetStatus}
										</button>
									{/each}
								</div>
							</CardContent>
						</Card>
					{/each}
				{/if}
			</div>
		</Tabs.Content>
	{/each}
</Tabs.Root>
