<script lang="ts">
	import { Button } from '$lib/components/ui/button';
	import { Input } from '$lib/components/ui/input';
	import { Label } from '$lib/components/ui/label';
	import { ColorPicker } from '$lib/components/ui/color-picker';
	import * as Select from '$lib/components/ui/select';
	import { TrashIcon, PlusIcon, GripVertical, CheckIcon, XIcon, InfoIcon } from '@lucide/svelte';
	import { flip } from 'svelte/animate';
	import {
		no_stages,
		add_stage,
		save_changes,
		cancel as cancelLabel,
		stage_manager_types_title,
		stage_manager_type_normal,
		stage_manager_type_success,
		stage_manager_type_failure
	} from '$lib/paraglide/messages';
	import type { CrmStage } from '$lib/types/crm.types';
	import { CrmStageType } from '$lib/enums/crm.enums';

	export let workingStages: CrmStage[] = [];
	export let isMobile = false;

	export let addStage: () => void;
	export let removeStage: (index: number) => void;
	export let saveStages: () => void;
	export let cancel: () => void;

	let draggedItemIndex: number | null = null;
	let dropTargetInfo: { index: number; position: 'before' | 'after' } | null = null;

	function handleDragStart(event: DragEvent, index: number) {
		if (event.dataTransfer) {
			event.dataTransfer.effectAllowed = 'move';
			event.dataTransfer.setData('text/plain', index.toString());
		}
		draggedItemIndex = index;
		dropTargetInfo = null;
	}

	function handleDragOverItem(event: DragEvent, overIndex: number) {
		event.preventDefault();
		if (event.dataTransfer) {
			event.dataTransfer.dropEffect = 'move';
		}

		if (overIndex === draggedItemIndex) {
			dropTargetInfo = null;
			return;
		}

		const targetElement = event.currentTarget as HTMLElement;
		const rect = targetElement.getBoundingClientRect();
		const mouseY = event.clientY;
		const midY = rect.top + rect.height / 2;

		dropTargetInfo = {
			index: overIndex,
			position: mouseY < midY ? 'before' : 'after'
		};
	}

	function handleDragLeaveListContainer() {
		dropTargetInfo = null;
	}

	function handleDropOnItem(event: DragEvent, droppedOnIndex: number) {
		event.preventDefault();
		if (draggedItemIndex === null || dropTargetInfo === null) {
			clearDragState();
			return;
		}

		if (dropTargetInfo.index !== droppedOnIndex) {
		}

		if (draggedItemIndex === dropTargetInfo.index) {
			clearDragState();
			return;
		}

		const itemToMove = workingStages[draggedItemIndex];
		let finalInsertionIndex = dropTargetInfo.index;

		if (dropTargetInfo.position === 'after') {
			finalInsertionIndex++;
		}

		workingStages.splice(draggedItemIndex, 1);

		if (draggedItemIndex < finalInsertionIndex) {
			finalInsertionIndex--;
		}

		workingStages.splice(finalInsertionIndex, 0, itemToMove);

		workingStages = workingStages.map((stage, index) => ({
			...stage,
			order: index
		}));

		clearDragState();
	}

	function handleDragEnd() {
		clearDragState();
	}

	function clearDragState() {
		draggedItemIndex = null;
		dropTargetInfo = null;
	}
</script>

<div class="space-y-4 {isMobile ? 'px-4' : 'py-4'}">
	{#if workingStages.length === 0}
		<p class="text-muted-foreground text-center text-sm">{no_stages()}</p>
	{:else}
		<div class="space-y-3" role="list" ondragleave={handleDragLeaveListContainer}>
			{#each workingStages as stage, index (stage.id)}
				<div
					role="listitem"
					class="flex items-center gap-3 transition-all duration-150"
					class:dragging={draggedItemIndex === index}
					class:border-t-2={dropTargetInfo?.index === index &&
						dropTargetInfo?.position === 'before' &&
						draggedItemIndex !== index}
					class:border-b-2={dropTargetInfo?.index === index &&
						dropTargetInfo?.position === 'after' &&
						draggedItemIndex !== index}
					class:border-blue-500={dropTargetInfo?.index === index &&
						draggedItemIndex !== index &&
						(dropTargetInfo?.position === 'before' || dropTargetInfo?.position === 'after')}
					class:opacity-50={draggedItemIndex === index}
					draggable="true"
					ondragstart={(event) => handleDragStart(event, index)}
					ondragover={(event) => handleDragOverItem(event, index)}
					ondrop={(event) => handleDropOnItem(event, index)}
					ondragend={handleDragEnd}
					animate:flip={{ duration: 200 }}
				>
					<div class="cursor-move p-2">
						<GripVertical class="text-muted-foreground h-5 w-5" />
					</div>
					<div class="flex-1">
						<Label for={`stage-name-${isMobile ? 'mobile-' : ''}${index}`} class="sr-only"
							>stage name</Label
						>
						<Input
							id={`stage-name-${isMobile ? 'mobile-' : ''}${index}`}
							bind:value={stage.name}
							placeholder="новая стадия"
							showEmojiPicker={true}
						/>
					</div>
					<div class="flex items-center">
						<Label for={`stage-color-btn-${isMobile ? 'mobile-' : ''}${index}`} class="sr-only"
							>stage color</Label
						>
						<ColorPicker bind:color={stage.color}>
							<div
								class="h-10 w-10 rounded-sm border border-gray-300 dark:border-gray-700"
								style="background-color: {stage.color};"
							></div>
						</ColorPicker>
					</div>
					<Select.Root
						type="single"
						value={stage.type}
						onValueChange={(v) => {
							if (v) {
								stage.type = v as CrmStageType;
								workingStages = workingStages;
							}
						}}
						disabled={index === 0}
					>
						<Select.Trigger
							class="bg-background flex h-10 w-16 items-center justify-center rounded-sm border"
						>
							{#if stage.type === CrmStageType.NORMAL}
								<InfoIcon class="h-4 w-4 text-blue-500" />
							{:else if stage.type === CrmStageType.SUCCESS}
								<CheckIcon class="h-4 w-4 text-green-500" />
							{:else}
								<XIcon class="h-4 w-4 text-red-500" />
							{/if}
						</Select.Trigger>
						<Select.Content>
							<Select.Item value={CrmStageType.NORMAL}>
								<InfoIcon class="h-8 w-4 text-blue-500" />
							</Select.Item>
							<Select.Item value={CrmStageType.SUCCESS}>
								<CheckIcon class="h-8 w-4 text-green-500" />
							</Select.Item>
							<Select.Item value={CrmStageType.FAILURE}>
								<XIcon class="h-8 w-4 text-red-500" />
							</Select.Item>
						</Select.Content>
					</Select.Root>
					<div class="flex gap-1">
						<Button
							variant="destructive"
							size="icon"
							onclick={() => removeStage(index)}
							disabled={workingStages.length <= 1}
							class="h-10 w-10"
							title="remove stage"
						>
							<TrashIcon class="h-4 w-4" />
						</Button>
					</div>
				</div>
			{/each}
		</div>
	{/if}

	<Button
		variant="outline"
		size="sm"
		onclick={addStage}
		class="flex w-full items-center justify-center gap-2 border-2 border-dashed py-6"
	>
		<PlusIcon class="h-5 w-5" />
		<span class="font-medium">{add_stage()}</span>
	</Button>

	{#if isMobile}
		<div class="mt-6 flex justify-end gap-2">
			<Button variant="outline" onclick={cancel} class="flex-1">{cancelLabel()}</Button>
			<Button onclick={saveStages} class="flex-1">{save_changes()}</Button>
		</div>
	{/if}

	<div class="rounded-md border border-blue-200 bg-blue-50 p-4 dark:bg-blue-900/20">
		<h4 class="mb-2 font-semibold text-blue-800 dark:text-blue-200">
			{stage_manager_types_title()}
		</h4>
		<ul class="space-y-1 text-sm text-blue-700 dark:text-blue-300">
			<li class="flex items-center gap-2">
				<InfoIcon class="h-4 w-4 text-blue-500" />
				{stage_manager_type_normal()}
			</li>
			<li class="flex items-center gap-2">
				<CheckIcon class="h-4 w-4 text-green-500" />
				{stage_manager_type_success()}
			</li>
			<li class="flex items-center gap-2">
				<XIcon class="h-4 w-4 text-red-500" />
				{stage_manager_type_failure()}
			</li>
		</ul>
	</div>
</div>

{#if !isMobile}
	<div class="flex justify-end gap-2">
		<Button variant="outline" onclick={cancel}>{cancelLabel()}</Button>
		<Button onclick={saveStages}>{save_changes()}</Button>
	</div>
{/if}

<style>
	.dragging {
		transform: scale(1.02);
		box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
	}
	[role='listitem'] {
		box-sizing: border-box;
	}
</style>
