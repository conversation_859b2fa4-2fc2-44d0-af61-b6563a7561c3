<script lang="ts">
	import DealCard from './DealCard.svelte';
	import { flip } from 'svelte/animate';
	import { Card, CardContent, CardHeader, CardTitle, CardFooter } from '$lib/components/ui/card';
	import { kanbanStageStore } from '$lib/stores/kanban-stage-store';
	import { createEventDispatcher } from 'svelte';
	import { crmStore } from '$lib/stores/crm-store';
	import type { CrmStage } from '$lib/types/crm.types';
	import { toast } from 'svelte-sonner';

	const dispatch = createEventDispatcher<{
		moveApi: { apiId: string; fromStatus: string; toStatus: string };
		deleteDeal: { dealId: string };
		enableDeal: { dealId: string };
		disableDeal: { dealId: string };
		resetDeal: { dealId: string };
	}>();

	let kanbanBoardElement: HTMLDivElement;

	function handleWheel(event: WheelEvent) {
		let el = event.target as HTMLElement | null;
		while (el && el !== kanbanBoardElement) {
			if (el.scrollHeight > el.clientHeight) {
				return;
			}
			el = el.parentElement;
		}

		event.preventDefault();
		if (kanbanBoardElement) {
			kanbanBoardElement.scrollLeft += event.deltaY;
		}
	}

	$: stagesArray = Array.isArray($kanbanStageStore) ? $kanbanStageStore : [];
	$: validStages = stagesArray.filter(
		(stage): stage is CrmStage & { id: string } => stage && typeof stage.id === 'string'
	);

	$: statuses = validStages.map((stage) => stage.id);
	$: stageNames = Object.fromEntries(validStages.map((stage) => [stage.id, stage.name]));

	$: dealsByStatus = (() => {
		const grouped = { ...$crmStore.groupedDeals };
		// ensure all status keys exist even if empty
		statuses.forEach((status) => {
			if (!grouped[status]) grouped[status] = [];
		});
		return grouped;
	})();

	let isDragging = false;
	let draggedDealId: string | null = null;
	let dragSourceColumn: string | null = null;
	let dragOverColumn: string | null = null;

	function handleDragStart(event: DragEvent, dealId: string, fromStatus: string) {
		isDragging = true;
		draggedDealId = dealId;
		dragSourceColumn = fromStatus;

		if (event.dataTransfer) {
			event.dataTransfer.setData('text/plain', dealId);
			event.dataTransfer.effectAllowed = 'move';
		}

		if (event.target instanceof HTMLElement) {
			event.target.classList.add('dragging');
		}
	}

	function handleDragOver(event: DragEvent, columnStatus: string) {
		event.preventDefault();

		if (event.dataTransfer) {
			event.dataTransfer.dropEffect = 'move';
		}

		dragOverColumn = columnStatus;
	}

	function handleDragEnter(event: DragEvent, columnStatus: string) {
		event.preventDefault();
		dragOverColumn = columnStatus;
	}

	function handleDragLeave(event: DragEvent) {
		if (
			event.target instanceof HTMLElement &&
			event.relatedTarget instanceof Element &&
			!event.relatedTarget.contains(event.target)
		) {
			dragOverColumn = null;
		}
	}

	async function handleDrop(event: DragEvent, toStatus: string) {
		event.preventDefault();
		if (!isDragging || !dragSourceColumn || !event.dataTransfer || dragSourceColumn === toStatus)
			return;

		const dealId = event.dataTransfer.getData('text/plain');

		crmStore.updateStage(dealId, toStatus).catch(() => {
			toast.error('failed to move deal, reverted');
		});

		dispatch('moveApi', {
			apiId: dealId,
			fromStatus: dragSourceColumn,
			toStatus
		});

		isDragging = false;
		draggedDealId = null;
		dragSourceColumn = null;
		dragOverColumn = null;
	}

	function handleDragEnd(event: DragEvent) {
		isDragging = false;
		draggedDealId = null;
		dragSourceColumn = null;
		dragOverColumn = null;

		if (event.target instanceof HTMLElement) {
			event.target.classList.remove('dragging');
		}
	}
</script>

<div
	class="kanban-board m-2 flex gap-4 overflow-x-auto pb-4"
	bind:this={kanbanBoardElement}
	onwheel={handleWheel}
>
	{#each validStages as stage (stage.id)}
		<div
			class="kanban-column w-80 flex-shrink-0 rounded-md border border-dashed border-slate-200 bg-slate-50 p-4 dark:border-slate-700 dark:bg-slate-900"
			class:drop-target={dragOverColumn === stage.id && dragSourceColumn !== stage.id}
			data-status={stage.id}
			ondragover={(e) => handleDragOver(e, stage.id)}
			ondragenter={(e) => handleDragEnter(e, stage.id)}
			ondragleave={handleDragLeave}
			ondrop={(e) => handleDrop(e, stage.id)}
			style="transition: all 0.2s ease; transform-origin: center"
			role="list"
		>
			<div class="mb-3 flex items-center justify-between">
				<h3 class="text-muted-foreground text-sm font-medium">
					{stageNames[stage.id] || stage.id}
				</h3>
				<div class="flex items-center gap-2">
					<span class="bg-muted rounded-full px-2 py-1 text-xs font-medium">
						{dealsByStatus[stage.id]?.length || 0}
					</span>
				</div>
			</div>

			<div class="flex-1 space-y-3 overflow-y-auto">
				{#if $crmStore.loading}
					{#each Array(2) as _}
						<Card class="opacity-70">
							<CardHeader class="pb-2">
								<CardTitle>
									<div class="bg-muted h-4 w-3/4 animate-pulse rounded-md"></div>
								</CardTitle>
							</CardHeader>
							<CardContent class="pb-2">
								<div class="flex flex-col gap-2">
									<div class="flex justify-between text-sm">
										<div class="bg-muted h-4 w-1/4 animate-pulse rounded-md"></div>
										<div class="bg-muted h-4 w-1/4 animate-pulse rounded-md"></div>
									</div>
									<div class="bg-muted h-4 w-3/4 animate-pulse rounded-md"></div>
								</div>
							</CardContent>
							<CardFooter>
								<div class="bg-muted h-9 w-full animate-pulse rounded-md"></div>
							</CardFooter>
						</Card>
					{/each}
				{:else}
					{#each dealsByStatus[stage.id] || [] as deal, i (deal.id)}
						<div
							class="draggable-item cursor-grab active:cursor-grabbing slide-up fade-in"
							style="--animation-delay: {i * 100}ms;"
							class:dragging={draggedDealId === deal.id}
							draggable="true"
							ondragstart={(e) => handleDragStart(e, deal.id, stage.id)}
							ondragend={handleDragEnd}
							animate:flip={{ duration: 200 }}
							role="listitem"
						>
							<DealCard {deal} {stage} />
						</div>
					{/each}
				{/if}
			</div>
		</div>
	{/each}
</div>

{#if $crmStore.hasMore}
	<div class="flex w-full justify-center py-4">
		<button
			class="rounded-md border bg-slate-100 px-4 py-2 text-sm hover:bg-slate-200 dark:bg-slate-800 dark:hover:bg-slate-700"
			onclick={() => crmStore.loadMore()}
		>
			load more deals
		</button>
	</div>
{/if}

<style>
	.kanban-column {
		min-height: 600px;
		max-height: 600px;
		min-width: 320px;
		display: flex;
		flex-direction: column;
		overflow-y: auto;
		transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
	}

	.drop-target {
		background-color: rgba(0, 88, 214, 0.08);
		border-color: #0058d6;
		transform: scale(1.02);
		box-shadow: 0 4px 6px -1px rgba(0, 88, 214, 0.1);
	}

	:global(.dark) .drop-target {
		background-color: rgba(0, 120, 255, 0.15);
		border-color: #0078ff;
		box-shadow: 0 4px 6px -1px rgba(0, 120, 255, 0.2);
	}

	.draggable-item {
		transition: transform 0.2s cubic-bezier(0.4, 0, 0.2, 1);
	}

	.dragging {
		opacity: 0.9;
		z-index: 20;
		transform: scale(1.05) rotate(2deg);
	}

	.draggable-item:active {
		cursor: grabbing;
	}

	.draggable-item:hover {
		cursor: grab;
	}
</style>
