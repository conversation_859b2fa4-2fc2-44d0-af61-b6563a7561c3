<script lang="ts">
	import { SettingsIcon } from '@lucide/svelte';
	import KanbanBoard from '$lib/components/dashboard/crm/KanbanBoard.svelte';
	import KanbanTabs from '$lib/components/dashboard/crm/KanbanTabs.svelte';
	import StageManagerDialog from '$lib/components/dashboard/crm/StageManagerDialog.svelte';
	import FunnelSelector from '$lib/components/dashboard/crm/FunnelSelector.svelte';
	import DeleteDealDialog from '$lib/components/dashboard/crm/DeleteDealDialog.svelte';
	import { kanbanStageStore } from '$lib/stores/kanban-stage-store';
	import { funnelStore } from '$lib/stores/funnel-store';
	import { useResponsive } from '$lib/utils/responsive';
	import { analytics } from '$lib/services/analytics';
	import { toast } from 'svelte-sonner';
	import { onDestroy, onMount } from 'svelte';
	import {
		manage_stages,
		stages_saved_success,
		stages_invalid_data,
		deal_add_failed,
		deal_added_success,
		no_stages_available,
		no_funnels_available,
		select_funnel_first
	} from '$lib/paraglide/messages';
	import { crmStore } from '$lib/stores/crm-store';

	$: hasStages = $kanbanStageStore && $kanbanStageStore.length > 0;
	$: hasFunnels = $funnelStore && $funnelStore.funnels.length > 0;

	const { isMobile } = useResponsive();

	function handleSaveStages(event: CustomEvent<{ stages: typeof $kanbanStageStore }>) {
		if (Array.isArray(event.detail.stages)) {
			kanbanStageStore.saveStages(event.detail.stages);

			// Track CRM configuration
			analytics.trackCrmFeatureUsed('stages_configured', '');

			toast.success(stages_saved_success());
		} else {
			toast.error(stages_invalid_data());
			console.error('Invalid stages data:', event.detail.stages);
		}
	}

	async function handleAddDeal(event: CustomEvent<{ name: string; stageId: string }>) {
		try {
			const { name, stageId } = event.detail;
			await crmStore.addDeal(name, stageId);

			// Track deal creation
			analytics.trackDealInteraction('deal_created', undefined, '');

			toast.success(deal_added_success({ name }));
		} catch (error) {
			toast.error(deal_add_failed());
			console.error('Failed to add deal:', error);
		}
	}

	let deleteDialogOpen = false;
	let dealIdToDelete: string | null = null;

	function handleDeleteDeal(event: CustomEvent<{ dealId: string }>) {
		dealIdToDelete = event.detail.dealId;
		deleteDialogOpen = true;
	}

	onMount(() => {
		// Track CRM feature access
		analytics.trackCrmFeatureUsed('deals_tab_opened', '');
		crmStore.reload();
		funnelStore.reload();
		kanbanStageStore.cleanup();
		loadStages();
	});

	onDestroy(() => {
		crmStore.cleanup();
		kanbanStageStore.cleanup();
		funnelStore.cleanup();
	});

	async function loadStages() {
		const stages = $kanbanStageStore;
		if (!stages || stages.length === 0) {
			await kanbanStageStore.reload();
		}
	}
</script>

<div class="overflow-hidden rounded-md border">
	<div class="flex items-center justify-between bg-slate-50 p-2 dark:bg-slate-900">
		<div>
			<FunnelSelector />
		</div>
		<button
			onclick={() => {
				if (!$funnelStore.activeFunnelId) {
					toast.error(select_funnel_first());
					return;
				}
				kanbanStageStore.openStageManager();
			}}
			class="flex items-center gap-1 rounded-md bg-slate-100 px-3 py-2 text-xs font-medium text-slate-700 hover:bg-slate-200 dark:bg-slate-800 dark:text-slate-300 dark:hover:bg-slate-700"
			data-ph-capture-attribute-action="crm-stage-management"
			data-ph-capture-attribute-has-active-funnel={!!$funnelStore.activeFunnelId}
			data-ph-capture-attribute-funnel-id={$funnelStore.activeFunnelId || 'none'}
		>
			<SettingsIcon class="h-4 w-4" />
			{manage_stages()}
		</button>
	</div>

	{#if !$funnelStore.loading && !hasFunnels}
		<div class="p-8 text-center text-slate-400 dark:text-slate-500">
			{no_funnels_available()}
		</div>
	{:else if !$crmStore.loading && !hasStages}
		<div class="p-8 text-center text-slate-400 dark:text-slate-500">
			{no_stages_available()}
		</div>
	{:else if $isMobile}
		<KanbanTabs on:addDeal={handleAddDeal} on:deleteDeal={handleDeleteDeal} />
	{:else}
		<KanbanBoard on:addDeal={handleAddDeal} on:deleteDeal={handleDeleteDeal} />
	{/if}

	<StageManagerDialog on:saveStages={handleSaveStages} />
	<DeleteDealDialog bind:open={deleteDialogOpen} dealId={dealIdToDelete ?? ''} />
</div>
