<script lang="ts">
	import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '$lib/components/ui/card';
	import { status, unnamed } from '$lib/paraglide/messages';
	import { ClockIcon, Trash2, Settings } from '@lucide/svelte';
	import { formatDateTime } from '$lib/utils/date';
	import { Button } from '$lib/components/ui/button';
	import IconEnable from '~icons/lucide/check-circle';
	import IconDisable from '~icons/lucide/x-circle';
	import IconReset from '~icons/lucide/refresh-cw';
	import IconTrash from '~icons/lucide/trash-2';
	import LogosWhatsappIcon from '~icons/logos/whatsapp-icon';
	import { toast } from 'svelte-sonner';
	import ResetMessagesDialog from '../ResetMessagesDialog.svelte';
	import { createWhatsappLink } from '$lib/utils/whatsapp';
	import { ResetAction } from '$lib/constants/reset-actions';
	import * as DropdownMenu from '$lib/components/ui/dropdown-menu';
	import { crmStore } from '$lib/stores/crm-store';
	import type { CrmDeal, CrmStage } from '$lib/types/crm.types';
	import { createEventDispatcher } from 'svelte';

	export let deal: CrmDeal;
	export let stage: CrmStage;

	const dispatch = createEventDispatcher<{
		deleteDeal: { dealId: string };
		enableDeal: { dealId: string };
		disableDeal: { dealId: string };
		resetDeal: { dealId: string };
	}>();

	let processingDeals = new Set<string>();
	let showResetDialog = false;
	let resetAction: { type: ResetAction | null; dealId: string } = { type: null, dealId: '' };

	function getDealInitial(deal: CrmDeal): string {
		return deal.name?.[0]?.toUpperCase() || 'A';
	}

	async function enableDeal(channelId: string, dealId: string, userId: string) {
		if (processingDeals.has(dealId)) return;
		processingDeals.add(dealId);
		try {
			await crmStore.setDealActive(dealId, true);
			toast.success('deal enabled');
		} catch (e) {
			console.error(e);
			toast.error('error enabling deal');
		} finally {
			processingDeals.delete(dealId);
		}
	}

	async function disableDeal(channelId: string, dealId: string, userId: string) {
		if (processingDeals.has(dealId)) return;
		processingDeals.add(dealId);
		try {
			await crmStore.setDealActive(dealId, false);
			toast.success('deal disabled');
		} catch (e) {
			console.error(e);
			toast.error('error disabling deal');
		} finally {
			processingDeals.delete(dealId);
		}
	}

	function openResetDialog(type: ResetAction, dealId: string) {
		resetAction = { type, dealId };
		showResetDialog = true;
	}

	function handleResetConfirm() {
		if (!resetAction.type || !resetAction.dealId) return;

		if (resetAction.type === ResetAction.Reset) {
			dispatch('resetDeal', { dealId: resetAction.dealId });
		}

		showResetDialog = false;
	}

	function openWhatsappChat(deal: CrmDeal) {
		window.open(createWhatsappLink(deal.user_identifier), '_blank', 'noopener,noreferrer');
	}

	function getStatusBorderColor(stage: CrmStage): string {
		return stage ? stage.color : '#0058D6';
	}
</script>

<Card
	class="crm-card border-l-4 shadow-sm"
	style="border-left-color: {getStatusBorderColor(stage)}"
>
	<CardHeader class="p-3 pb-1">
		<div class="flex items-center gap-2">
			<div
				class="flex h-8 w-8 items-center justify-center rounded-full bg-blue-100 text-blue-600 dark:bg-blue-900 dark:text-blue-200"
			>
				{getDealInitial(deal)}
			</div>
			<CardTitle class="text-sm font-medium">
				{deal.user_identifier || unnamed()}
			</CardTitle>
			<div class="ml-auto">
				<DropdownMenu.Root>
					<DropdownMenu.Trigger>
						<Button
							variant="ghost"
							size="icon"
							class="h-6 w-6 items-center justify-center rounded-full text-center"
						>
							<Settings class="h-3.5 w-3.5 text-center text-green-500" />
						</Button>
					</DropdownMenu.Trigger>
					<DropdownMenu.Content>
						<DropdownMenu.Group>
							<DropdownMenu.Item
								onclick={() => openResetDialog(ResetAction.Reset, deal.id)}
								disabled={processingDeals.has(deal.id)}
								class="text-orange-600"
							>
								<IconTrash class="mr-2 h-3.5 w-3.5" />
								<span class="text-xs">reset</span>
							</DropdownMenu.Item>

							<DropdownMenu.Item onclick={() => openWhatsappChat(deal)} class="text-green-600">
								<LogosWhatsappIcon class="mr-2 h-3.5 w-3.5" />
								<span class="text-xs">whatsapp</span>
							</DropdownMenu.Item>

							{#if deal.is_active}
								<DropdownMenu.Item
									onclick={() => disableDeal(deal.on_ai_id, deal.id, deal.user_identifier)}
									disabled={processingDeals.has(deal.id)}
									class="text-red-600"
								>
									<IconDisable class="mr-2 h-3.5 w-3.5" />
									<span class="text-xs">disable</span>
								</DropdownMenu.Item>
							{:else}
								<DropdownMenu.Item
									onclick={() => enableDeal(deal.on_ai_id, deal.id, deal.user_identifier)}
									disabled={processingDeals.has(deal.id)}
									class="text-green-600"
								>
									<IconEnable class="mr-2 h-3.5 w-3.5" />
									<span class="text-xs">enable</span>
								</DropdownMenu.Item>
							{/if}

							<DropdownMenu.Separator />
							<DropdownMenu.Item
								onclick={() => dispatch('deleteDeal', { dealId: deal.id })}
								class="text-destructive"
							>
								<Trash2 class="mr-2 h-3.5 w-3.5" />
								<span class="text-xs">delete</span>
							</DropdownMenu.Item>
						</DropdownMenu.Group>
					</DropdownMenu.Content>
				</DropdownMenu.Root>
			</div>
		</div>
	</CardHeader>
	<CardContent class="p-3 pb-1 pt-1">
		<div class="flex flex-col gap-2">
			<div class="flex justify-between text-xs">
				<span class="text-muted-foreground">{status()}:</span>
				<span
					class={deal.is_active
						? 'font-medium text-[#0058D6] dark:text-blue-400'
						: 'font-medium text-red-500 dark:text-red-400'}
				>
					{deal.is_active ? 'on.ai' : 'off'}
				</span>
			</div>
			<div class="text-muted-foreground flex items-center gap-1 text-xs">
				<ClockIcon class="no-drag" size={12} />
				<span>{formatDateTime(new Date(deal.created_at))}</span>
			</div>
		</div>
	</CardContent>
</Card>

<ResetMessagesDialog
	bind:open={showResetDialog}
	action={resetAction.type}
	userId={resetAction.dealId}
	onConfirm={handleResetConfirm}
/>
