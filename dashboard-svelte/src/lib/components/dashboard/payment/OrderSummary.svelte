<script lang="ts">
	import {
		payment_order_summary,
		payment_selected_plan,
		payment_duration,
		payment_monthly_rate,
		payment_per_month_rate,
		payment_total
	} from '$lib/paraglide/messages';
	export let selectedPlan: string;
	export let duration: string;
	export let monthlyRate: number;
	export let price: number;
</script>

<h3 class="mb-4 text-lg font-medium">{payment_order_summary()}</h3>
<div class="mb-6 space-y-3">
	<div class="flex justify-between">
		<span class="text-muted-foreground">{payment_selected_plan()}</span>
		<span class="font-medium">{selectedPlan}</span>
	</div>
	<div class="flex justify-between">
		<span class="text-muted-foreground">{payment_duration()}</span>
		<span class="font-medium">{duration}</span>
	</div>
	<div class="flex justify-between">
		<span class="text-muted-foreground">{payment_monthly_rate()}</span>
		<span class="font-medium">${monthlyRate}{payment_per_month_rate()}</span>
	</div>
	<div class="mt-4 flex justify-between border-t pt-4">
		<span class="text-lg font-medium">{payment_total()}</span>
		<span class="text-2xl font-bold">${price}</span>
	</div>
</div>
