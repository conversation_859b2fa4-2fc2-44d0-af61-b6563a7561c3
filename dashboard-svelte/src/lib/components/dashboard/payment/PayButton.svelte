<script lang="ts">
	import { Button } from '$lib/components/ui/button';
	import { payment_proceed_to_payment } from '$lib/paraglide/messages';
	import { analytics } from '$lib/services/analytics';

	export let onProceed: () => void;
	export let planId: string = '';
	export let price: number = 0;
	export let term: 'monthly' | 'yearly' = 'monthly';
	export let duration: number = 1;

	function handleProceed() {
		// Track payment initiation
		analytics.trackPaymentInitiated(planId, price, term, duration);

		onProceed();
	}
</script>

<Button
	class="w-full"
	size="lg"
	variant="default"
	onclick={handleProceed}
	data-ph-capture-attribute-plan-id={planId}
	data-ph-capture-attribute-plan-price={price}
	data-ph-capture-attribute-plan-term={term}
	data-ph-capture-attribute-plan-duration={duration}
	data-ph-capture-attribute-action="payment-initiation"
>
	{payment_proceed_to_payment()}
</Button>
