<script lang="ts">
	import type { Plan, Term } from '$lib/utils/pricing';

	export let selectedPlan: Plan;
	export let selectedTerm: Term;
</script>

<!-- minimal placeholder content -->
<div class="flex gap-4">
	<!-- basic plan selection UI placeholder -->
	<button class="border p-2" onclick={() => (selectedPlan = 'open')}>open</button>
	<button class="border p-2" onclick={() => (selectedPlan = 'omni')}>omni</button>

	<!-- term toggle -->
	<button class="border p-2" onclick={() => (selectedTerm = 'monthly')}>monthly</button>
	<button class="border p-2" onclick={() => (selectedTerm = 'annual')}>annual</button>
</div>
