<script lang="ts">
	import { createEventDispatcher } from 'svelte';
	import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger, TabsContent } from '$lib/components/ui/tabs';
	import { Input } from '$lib/components/ui/input';
	import { Check } from '@lucide/svelte';
	import { analytics } from '$lib/services/analytics';
	import {
		MONTH_DURATIONS,
		YEAR_DURATIONS,
		type PeriodType,
		type MonthDuration,
		type YearDuration,
		calculateMonthlyPrice,
		calculateYearlyPrice,
		getSalePercentage,
		hasSale
	} from '$lib/utils/pricing';
	import {
		payment_monthly,
		payment_annually,
		payment_month,
		payment_months,
		payment_custom_duration,
		payment_months_for,
		payment_year,
		payment_years,
		payment_billed_every,
		payment_save
	} from '$lib/paraglide/messages';

	export let selectedPeriodType: PeriodType;
	export let selectedMonthDuration: MonthDuration;
	export let selectedYearDuration: YearDuration;
	export let customMonths: string;
	export let basePrice: number;

	const dispatch = createEventDispatcher();

	function onSelectMonthly(months: MonthDuration) {
		const price = calculateMonthlyPrice(months, basePrice);

		// Track plan selection
		analytics.trackPlanSelected(`monthly-${months}`, price, 'monthly', months);

		dispatch('selectedPeriodType', 'monthly');
		dispatch('selectedMonthDuration', months);
		dispatch('customMonths', '');
	}

	function onSelectYearly(years: YearDuration) {
		const price = calculateYearlyPrice(years, basePrice);

		// Track plan selection
		analytics.trackPlanSelected(`yearly-${years}`, price, 'yearly', years * 12);

		dispatch('selectedPeriodType', 'yearly');
		dispatch('selectedYearDuration', years);
	}

	function handleCustomMonths(event: Event) {
		const input = event.target as HTMLInputElement;
		const months = parseInt(input.value);

		if (!isNaN(months) && months > 0) {
			const price = calculateMonthlyPrice(months, basePrice);

			// Track custom plan selection
			analytics.trackPlanSelected(`custom-${months}`, price, 'monthly', months);
		}

		dispatch('selectedPeriodType', 'monthly');
		dispatch('customMonths', input.value);
	}
</script>

<div class="bg-card rounded-lg border p-6 shadow-sm">
	<Tabs
		value={selectedPeriodType}
		onValueChange={(value) => dispatch('selectedPeriodType', value as PeriodType)}
	>
		<TabsList class="mb-6 grid w-full grid-cols-2">
			<TabsTrigger value="monthly" class="text-base">{payment_monthly()}</TabsTrigger>
			<TabsTrigger value="yearly" class="text-base">{payment_annually()}</TabsTrigger>
		</TabsList>
		<TabsContent value="monthly">
			<div class="grid grid-cols-2 gap-4 sm:grid-cols-5">
				{#each MONTH_DURATIONS as months}
					<button
						class="flex flex-col items-center rounded-lg border p-4 transition-colors {selectedMonthDuration ===
							months && !customMonths
							? 'border-primary bg-primary/5 ring-primary ring-1'
							: 'hover:border-primary/50'}"
						onclick={() => onSelectMonthly(months as MonthDuration)}
						data-ph-capture-attribute-plan-id={`monthly-${months}`}
						data-ph-capture-attribute-plan-price={calculateMonthlyPrice(months, basePrice)}
						data-ph-capture-attribute-plan-term="monthly"
						data-ph-capture-attribute-plan-duration={months}
						data-ph-capture-attribute-action="plan-selection"
						data-ph-capture-attribute-base-price={basePrice}
						data-ph-capture-attribute-has-sale={hasSale('monthly', months)}
					>
						<span class="font-medium"
							>{months} {months === 1 ? payment_month() : payment_months()}</span
						>
						<span class="text-muted-foreground mt-1 text-sm"
							>${calculateMonthlyPrice(months, basePrice)}</span
						>
						{#if hasSale('monthly', months)}
							<span class="mt-2 rounded-full bg-green-100 px-2 py-0.5 text-xs text-green-800">
								{payment_save()}{getSalePercentage('monthly', months)}
							</span>
						{/if}
						{#if selectedMonthDuration === months && !customMonths}
							<div class="absolute right-2 top-2">
								<Check class="text-primary size-4" />
							</div>
						{/if}
					</button>
				{/each}
				<div
					class="mt-6 rounded-lg border border-dashed p-4 {customMonths &&
					parseInt(customMonths) > 0
						? 'border-primary'
						: 'border-border'}"
				>
					<p class="mb-3 text-sm font-medium">{payment_custom_duration()}</p>
					<div class="flex items-center gap-3">
						<Input
							type="number"
							value={customMonths}
							oninput={handleCustomMonths}
							min="1"
							max="24"
							placeholder="12"
							class="w-24 text-center {customMonths && parseInt(customMonths) > 0
								? 'ring-primary ring-1'
								: ''}"
							data-ph-capture-attribute-plan-type="custom-monthly"
							data-ph-capture-attribute-action="custom-plan-input"
							data-ph-capture-attribute-base-price={basePrice}
						/>
						<span class="text-sm">
							{payment_months_for()}
							<span class="font-semibold"
								>${customMonths && !isNaN(parseInt(customMonths)) && parseInt(customMonths) > 0
									? calculateMonthlyPrice(parseInt(customMonths), basePrice)
									: ''}</span
							>
						</span>
					</div>
				</div>
			</div>
		</TabsContent>
		<TabsContent value="yearly">
			<div class="grid gap-4">
				{#each YEAR_DURATIONS as years}
					<button
						class="flex w-full items-center justify-between rounded-lg border p-5 transition-colors {selectedYearDuration ===
						years
							? 'border-primary bg-primary/5 ring-primary ring-1'
							: 'hover:border-primary/50'}"
						onclick={() => onSelectYearly(years as YearDuration)}
						data-ph-capture-attribute-plan-id={`yearly-${years}`}
						data-ph-capture-attribute-plan-price={calculateYearlyPrice(years, basePrice)}
						data-ph-capture-attribute-plan-term="yearly"
						data-ph-capture-attribute-plan-duration={years * 12}
						data-ph-capture-attribute-action="plan-selection"
						data-ph-capture-attribute-base-price={basePrice}
						data-ph-capture-attribute-has-sale={hasSale('yearly', years)}
					>
						<div>
							<div class="text-lg font-medium">
								{years}{years === 1 ? payment_year() : payment_years()}
							</div>
							<div class="text-muted-foreground mt-1 text-sm">
								{payment_billed_every()}{years}{years === 1 ? payment_year() : payment_years()}
							</div>
						</div>
						<div class="text-right">
							<div class="text-xl font-bold">${calculateYearlyPrice(years, basePrice)}</div>
							{#if hasSale('yearly', years)}
								<div class="mt-1 rounded-full bg-green-100 px-3 py-1 text-xs text-green-800">
									{payment_save()}{getSalePercentage('yearly', years)}
								</div>
							{/if}
						</div>
						{#if selectedYearDuration === years}
							<div class="absolute right-4 top-4">
								<Check class="text-primary size-5" />
							</div>
						{/if}
					</button>
				{/each}
			</div>
		</TabsContent>
	</Tabs>
</div>
