<script lang="ts">
	import { Button } from '$lib/components/ui/button';
	import { knowledgeStore } from '$lib/stores/knowledge-store';
	import { toast } from 'svelte-sonner';
	import {
		knowledge_delete_success,
		knowledge_delete_error,
		knowledge_delete_confirm,
		cancel,
		knowledge_delete,
		knowledge_deleting
	} from '$lib/paraglide/messages';

	export let onClose: () => void;
	export let knowledgeId: string | null = null;
	export const type: 'text' | 'document' = 'text';
	export let onDeleted: () => void = () => {};

	let isDeleting = false;

	async function handleDelete() {
		if (!knowledgeId) {
			toast.error(knowledge_delete_error());
			onClose();
			return;
		}

		isDeleting = true;
		try {
			await knowledgeStore.deleteKnowledge(knowledgeId);
			toast.success(knowledge_delete_success());
			onDeleted();
			onClose();
		} catch (error) {
			console.error('Error deleting knowledge:', error);
			toast.error(knowledge_delete_error());
		} finally {
			isDeleting = false;
		}
	}
</script>

<div>
	<p class="text-muted-foreground text-sm">
		{knowledge_delete_confirm()}
	</p>
	<div class="flex justify-end gap-3 pt-4">
		<Button variant="outline" onclick={onClose} disabled={isDeleting}>
			{cancel()}
		</Button>
		<Button variant="destructive" onclick={handleDelete} disabled={isDeleting}>
			{isDeleting ? knowledge_deleting() : knowledge_delete()}
		</Button>
	</div>
</div>
