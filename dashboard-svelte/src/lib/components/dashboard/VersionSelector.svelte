<script lang="ts">
	import * as Select from '$lib/components/ui/select';
	import { Button } from '$lib/components/ui/button';
	import SwapIcon from '~icons/lucide/arrow-left-right';
	import {
		knowledge_version as version_label,
		knowledge_select_version as select_version
	} from '$lib/paraglide/messages';

	export let predefinedVersions: { value: string; label: string; description: string }[] = [];
	export let leftVersion: string | undefined;
	export let rightVersion: string | undefined;
	export let onLeftVersionSelect: (value: string | undefined) => void;
	export let onRightVersionSelect: (value: string | undefined) => void;
	export let swapVersions: () => void;
</script>

<div class="flex items-center gap-2">
	<div class="flex-1">
		<Select.Root type="single" value={leftVersion} onValueChange={onLeftVersionSelect}>
			<Select.Trigger>
				{leftVersion ? version_label({ number: leftVersion }) : select_version()}
			</Select.Trigger>
			<Select.Content>
				<Select.Group>
					{#each predefinedVersions as version}
						<Select.Item value={version.value}>
							<div>
								<div>{version.label}</div>
								<div class="text-muted-foreground text-xs">{version.description}</div>
							</div>
						</Select.Item>
					{/each}
				</Select.Group>
			</Select.Content>
		</Select.Root>
	</div>
	<Button variant="ghost" size="icon" onclick={swapVersions}>
		<SwapIcon />
	</Button>
	<div class="flex-1">
		<Select.Root type="single" value={rightVersion} onValueChange={onRightVersionSelect}>
			<Select.Trigger>
				{rightVersion ? version_label({ number: rightVersion }) : select_version()}
			</Select.Trigger>
			<Select.Content>
				<Select.Group>
					{#each predefinedVersions as version}
						<Select.Item value={version.value}>
							<div>
								<div>{version.label}</div>
								<div class="text-muted-foreground text-xs">{version.description}</div>
							</div>
						</Select.Item>
					{/each}
				</Select.Group>
			</Select.Content>
		</Select.Root>
	</div>
</div>
