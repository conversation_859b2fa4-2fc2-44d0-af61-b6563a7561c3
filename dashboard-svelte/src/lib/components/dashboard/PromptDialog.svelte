<script lang="ts">
	import * as Dialog from '$lib/components/ui/dialog';
	import * as Drawer from '$lib/components/ui/drawer';
	import PromptForm from './PromptForm.svelte';
	import { useResponsive } from '$lib/utils/responsive';
	import type { PromptType } from '$lib/stores/admin-prompt-store';
	import { createEventDispatcher } from 'svelte';

	const dispatch = createEventDispatcher();

	export let open = false;
	export let type: PromptType;
	export let title: string;
	export let description: string;
	export let label: string;
	export let placeholder: string;
	export let maxLength: number;
	export let successMessage: string;
	export let formError: string | undefined;
	export let store: any;

	const { isMobile } = useResponsive();
	$: isDesktop = !$isMobile;

	function handleClose() {
		open = false;
	}

	$: if (!open) {
		dispatch('close');
	}
</script>

{#if isDesktop}
	<Dialog.Root bind:open>
		<Dialog.Content class="m-0 flex h-screen w-screen max-w-none flex-col rounded-none p-6">
			<div class="absolute right-4 top-4">
				<Dialog.Close />
			</div>
			<Dialog.Header class="pb-2">
				<Dialog.Title>{title}</Dialog.Title>
				<Dialog.Description>{description}</Dialog.Description>
			</Dialog.Header>
			<div class="flex-1 overflow-hidden">
				<PromptForm
					{type}
					{label}
					{placeholder}
					{maxLength}
					{successMessage}
					{formError}
					{store}
					onClose={handleClose}
				/>
			</div>
		</Dialog.Content>
	</Dialog.Root>
{:else}
	<Drawer.Root bind:open>
		<Drawer.Content class="flex h-screen flex-col">
			<Drawer.Header class="text-left">
				<Drawer.Title>{title}</Drawer.Title>
				<Drawer.Description>{description}</Drawer.Description>
			</Drawer.Header>
			<div class="flex-1 overflow-hidden px-4">
				<PromptForm
					{type}
					{label}
					{placeholder}
					{maxLength}
					{successMessage}
					{formError}
					{store}
					onClose={handleClose}
				/>
			</div>
		</Drawer.Content>
	</Drawer.Root>
{/if}
