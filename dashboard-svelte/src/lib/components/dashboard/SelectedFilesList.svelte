<script lang="ts">
	import { Button } from '$lib/components/ui/button';
	import { Progress } from '$lib/components/ui/progress/index.js';
	import MaterialSymbolsClose from '~icons/material-symbols/close';
	import MaterialSymbolsDescription from '~icons/material-symbols/description';
	import MaterialSymbolsDownload from '~icons/material-symbols/download';
	import type { FileUploadStatus } from '$lib/stores/knowledge-form-ui-store';
	import {
		file_upload_selected_files,
		file_upload_clear_all,
		file_upload_completed,
		file_upload_failed,
		file_upload_remove_file,
		file_upload_download_file
	} from '$lib/paraglide/messages';
	import { analytics } from '$lib/services/analytics';

	export let selectedFiles: File[];
	export let fileUploadStatus: Record<string, FileUploadStatus>;
	export let onClearAll: () => void;
	export let onRemoveFile: (file: File) => void;
	export let onDownloadFile: (fileName: string, knowledgeId: string) => void;

	function formatFileSize(bytes: number): string {
		if (bytes === 0) return '0 Bytes';
		const k = 1024;
		const sizes = ['Bytes', 'KB', 'MB', 'GB'];
		const i = Math.floor(Math.log(bytes) / Math.log(k));
		return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
	}

	function handleClearAll() {
		analytics.trackButtonClicked('clear_all_files', 'file_upload', 'destructive');
		analytics.trackBulkAction('clear_files', selectedFiles.length, true);
		onClearAll();
	}

	function handleRemoveFile(file: File) {
		analytics.trackButtonClicked('remove_file', 'file_upload', 'destructive');
		analytics.trackFileUpload(file.name, file.size, file.type, false);
		onRemoveFile(file);
	}

	function handleDownloadFile(fileName: string, knowledgeId: string) {
		analytics.trackButtonClicked('download_file', 'file_upload', 'outline');
		analytics.trackDownloadStarted(fileName, 'knowledge_document');
		onDownloadFile(fileName, knowledgeId);
	}
</script>

{#if selectedFiles.length > 0}
	<div class="space-y-2">
		<div class="flex items-center justify-between">
			<h4 class="text-sm font-medium">
				{file_upload_selected_files({ count: selectedFiles.length })}
			</h4>
			<Button variant="ghost" size="sm" onclick={handleClearAll} class="h-8 px-2 text-xs">
				{file_upload_clear_all()}
			</Button>
		</div>
		<div class="max-h-60 space-y-2 overflow-y-auto">
			{#each selectedFiles as file, index (file.name)}
				<div class="bg-muted/20 flex items-center gap-3 rounded-md border p-3">
					<div class="flex-shrink-0">
						<MaterialSymbolsDescription class="text-muted-foreground h-5 w-5" />
					</div>
					<div class="min-w-0 flex-1">
						<div class="flex items-center justify-between">
							<p class="truncate text-sm font-medium">{file.name}</p>
							<div class="flex items-center gap-1">
								{#if fileUploadStatus[file.name]?.status === 'success' && fileUploadStatus[file.name]?.knowledgeId}
									<Button
										variant="ghost"
										size="sm"
										onclick={() =>
											handleDownloadFile(file.name, fileUploadStatus[file.name].knowledgeId || '')}
										class="h-6 w-6 p-0 hover:bg-green-100"
										title={file_upload_download_file({ fileName: file.name })}
									>
										<MaterialSymbolsDownload class="h-3 w-3 text-green-600" />
									</Button>
								{/if}
								<Button
									variant="ghost"
									size="sm"
									onclick={() => handleRemoveFile(file)}
									class="hover:bg-destructive/10 hover:text-destructive h-6 w-6 p-0"
									title={file_upload_remove_file({ fileName: file.name })}
								>
									<MaterialSymbolsClose class="h-4 w-4" />
								</Button>
							</div>
						</div>
						<div class="text-muted-foreground flex items-center justify-between text-xs">
							<span>{formatFileSize(file.size)}</span>
							{#if fileUploadStatus[file.name]}
								<span class="capitalize">
									{fileUploadStatus[file.name].status}
									{#if fileUploadStatus[file.name].status === 'uploading'}
										({fileUploadStatus[file.name].progress}%)
									{/if}
								</span>
							{/if}
						</div>
						{#if fileUploadStatus[file.name]?.status === 'uploading'}
							<div class="mt-2">
								<Progress value={fileUploadStatus[file.name].progress} max={100} class="h-1" />
							</div>
						{:else if fileUploadStatus[file.name]?.status === 'error'}
							<p class="text-destructive mt-1 text-xs">
								{fileUploadStatus[file.name].error || file_upload_failed()}
							</p>
						{:else if fileUploadStatus[file.name]?.status === 'success'}
							<p class="mt-1 text-xs text-green-600">{file_upload_completed()}</p>
						{/if}
					</div>
				</div>
			{/each}
		</div>
	</div>
{/if}
