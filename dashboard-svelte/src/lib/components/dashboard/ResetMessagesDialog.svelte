<script lang="ts">
	import * as Dialog from '$lib/components/ui/dialog';
	import * as Drawer from '$lib/components/ui/drawer';
	import { useResponsive } from '$lib/utils/responsive';
	import { users_reset_dialog_title, users_reset_all_dialog_title } from '$lib/paraglide/messages';
	import ResetMessagesForm from './ResetMessagesForm.svelte';
	import { ResetAction } from '$lib/constants/reset-actions';

	export let open = false;
	export let action: ResetAction | null = null;
	export let userId = '';
	export let onConfirm: () => void;
	export let isProcessing = false;

	const { isMobile } = useResponsive();
	$: isDesktop = !$isMobile;

	function getDialogTitle() {
		switch (action) {
			case ResetAction.Reset:
				return users_reset_dialog_title();
			case ResetAction.ResetAll:
				return users_reset_all_dialog_title();
			default:
				return users_reset_dialog_title();
		}
	}
</script>

{#if isDesktop}
	<Dialog.Root bind:open>
		<Dialog.Content>
			<Dialog.Header>
				<Dialog.Title>{getDialogTitle()}</Dialog.Title>
			</Dialog.Header>
			<ResetMessagesForm
				{action}
				{userId}
				{isProcessing}
				onClose={() => (open = false)}
				{onConfirm}
			/>
		</Dialog.Content>
	</Dialog.Root>
{:else}
	<Drawer.Root bind:open>
		<Drawer.Content>
			<Drawer.Header class="text-left">
				<Drawer.Title>{getDialogTitle()}</Drawer.Title>
			</Drawer.Header>
			<div class="px-4">
				<ResetMessagesForm
					{action}
					{userId}
					{isProcessing}
					onClose={() => (open = false)}
					{onConfirm}
				/>
			</div>
		</Drawer.Content>
	</Drawer.Root>
{/if}
