<script lang="ts">
	import { Bell, CheckCircle, AlertCircle, Info } from '@lucide/svelte';
	import { buttonVariants } from '$lib/components/ui/button';
	import * as DropdownMenu from '$lib/components/ui/dropdown-menu/index.js';
	import { cn } from '$lib/utils';
	import { onMount } from 'svelte';
	import { notificationStore } from '$lib/stores/notification-store';
	import { goto } from '$app/navigation';
	import { formatDistanceToNow } from 'date-fns';
	import type { Notification } from '$lib/types/notification.types';

	let storeData = $derived($notificationStore);
	let notifications = $derived(storeData.notifications);
	let unreadCount = $derived(notifications.filter((n) => !n.is_read).length);
	let isLoading = $derived(storeData.loading);

	onMount(() => {
		notificationStore.load({ limit: 100, offset: 0, is_read: false });
	});

	function markAsRead(id: string) {
		notificationStore.markAsRead(id);
	}

	function handleNotificationClick(n: Notification) {
		markAsRead(n.id);
		if (n.metadata?.url) {
			goto(n.metadata.url);
		}
	}

	function getNotificationIcon(type: string) {
		switch (type) {
			case 'success':
				return CheckCircle;
			case 'error':
				return AlertCircle;
			default:
				return Info;
		}
	}

	function formatTime(timestamp: string) {
		try {
			return formatDistanceToNow(new Date(timestamp), { addSuffix: true });
		} catch (e) {
			return '';
		}
	}
</script>

<DropdownMenu.Root>
	<DropdownMenu.Trigger
		class={cn(buttonVariants({ variant: 'ghost' }), 'relative')}
		data-ph-capture-attribute-action="notifications-dropdown"
		data-ph-capture-attribute-unread-count={unreadCount}
	>
		<Bell class="h-4 w-4" />
		{#if unreadCount}
			<span
				class="absolute -right-[0.1px] -top-[0.1px] flex h-4 w-4 items-center justify-center rounded-full text-[12px] text-white"
				>{unreadCount}</span
			>
		{/if}
	</DropdownMenu.Trigger>
	<DropdownMenu.Content class="max-h-96 w-80 overflow-auto p-0">
		<div class="bg-muted/30 border-b px-3 py-2">
			<div class="flex items-center justify-end">
				{#if unreadCount}
					<span class="bg-muted/50 rounded-full px-2 py-0.5 text-xs">{unreadCount} unread</span>
				{/if}
			</div>
		</div>

		{#if isLoading}
			<div class="text-muted-foreground p-4 text-center text-sm">loading notifications...</div>
		{:else if notifications.length === 0}
			<div class="text-muted-foreground p-4 text-center text-sm">no notifications</div>
		{:else}
			<div class="py-1">
				{#each notifications as n (n.id)}
					<DropdownMenu.Item
						onclick={() => handleNotificationClick(n)}
						class={cn('flex gap-2 border-b p-2 last:border-0', !n.is_read && 'bg-muted/30')}
						data-ph-capture-attribute-action="notification-click"
						data-ph-capture-attribute-notification-id={n.id}
						data-ph-capture-attribute-notification-type={n.type}
						data-ph-capture-attribute-notification-read={n.is_read}
					>
						<div class="flex-shrink-0 pt-1">
							{#if getNotificationIcon(n.type) === CheckCircle}
								<CheckCircle class="text-success h-4 w-4" />
							{:else if getNotificationIcon(n.type) === AlertCircle}
								<AlertCircle class="text-destructive h-4 w-4" />
							{:else}
								<Info class="text-muted-foreground h-4 w-4" />
							{/if}
						</div>
						<div class="flex flex-grow flex-col gap-1">
							<div class="flex items-start justify-between gap-2">
								<span class="text-sm font-medium">{n.title}</span>
								<span class="text-muted-foreground whitespace-nowrap text-[10px]"
									>{formatTime(n.created_at)}</span
								>
							</div>
							<span class="text-muted-foreground text-xs">{n.message}</span>
						</div>
					</DropdownMenu.Item>
				{/each}
			</div>
		{/if}

		{#if notifications.length > 0}
			<div class="bg-muted/30 border-t px-3 py-1">
				<a href="/dashboard/notifications" class="text-primary block w-full text-center text-xs">
					view all notifications
				</a>
			</div>
		{/if}
	</DropdownMenu.Content>
</DropdownMenu.Root>
