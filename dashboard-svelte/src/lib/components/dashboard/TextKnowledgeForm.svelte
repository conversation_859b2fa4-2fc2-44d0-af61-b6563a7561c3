<script lang="ts">
	import { Button } from '$lib/components/ui/button';
	import Textarea from '$lib/components/ui/textarea/textarea.svelte';
	import { Skeleton } from '$lib/components/ui/skeleton';
	import { DotIcon } from '@lucide/svelte';
	import MaterialSymbolsDownload from '~icons/material-symbols/download';
	import MaterialSymbolsDelete from '~icons/material-symbols/delete';
	import {
		knowledge_enter_content,
		knowledge_download_file,
		knowledge_delete,
		symbols,
		tokens
	} from '$lib/paraglide/messages';

	export let textInlineContent: string;
	export let canDownloadText: boolean;
	export let canDeleteText: boolean;
	export let onDownload: () => void;
	export let onDelete: () => void;
	export let maxLength: number = 40000;
	export let promptTokens: number = 0;
	export let isTokenLoading: boolean = false;
</script>

<div class="flex flex-1 flex-col gap-2">
	<label for="knowledge-content" class="text-sm font-medium">{knowledge_enter_content()}</label>
	<div class="flex flex-1 flex-col gap-2">
		<div class="h-full">
			<Textarea
				id="knowledge-content"
				name="knowledge-content"
				bind:value={textInlineContent}
				placeholder={knowledge_enter_content()}
				maxlength={maxLength}
				countLabel={symbols()}
				showCount={true}
				autoGrow={false}
				class="absolute h-full min-h-[580px] w-full resize-none"
			>
				<span slot="alongside-count" class="text-sm text-gray-400">
					{#if isTokenLoading}
						<Skeleton class="h-4 w-16" />
					{:else}
						<div class="flex items-center gap-1">
							<DotIcon class="h-4 w-4" />
							<span>{promptTokens} {tokens()}</span>
						</div>
					{/if}
				</span>
			</Textarea>
		</div>
	</div>
</div>

<div class="mt-2 flex gap-2">
	{#if canDownloadText}
		<Button type="button" size="sm" onclick={onDownload} variant="outline" class="flex-1">
			<MaterialSymbolsDownload class="mr-2 h-4 w-4" />
			{knowledge_download_file()}
		</Button>
	{/if}
</div>
