<script lang="ts">
	import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '$lib/components/ui/card';
	import { buttonVariants } from '$lib/components/ui/button';
	import { cn } from '$lib/utils';
	import { Skeleton } from '$lib/components/ui/skeleton/index.js';
	import { analytics } from '$lib/services/analytics';
	import {
		status,
		channels,
		management,
		new_onai,
		new_instruction_question,
		unnamed,
		error_generic,
		loading,
		failed_to_load_subscription
	} from '$lib/paraglide/messages';
	import { onAiStore } from '$lib/stores/onai-store';
	import { SettingsIcon } from '@lucide/svelte';
	import { SUBSCRIPTION_ENABLED } from '$lib/config/featureFlags';
	import { subscriptionService } from '$lib/services/subscription';
	import type { SubscriptionResponse } from '$lib/types/subscription.types';
	import {
		tokens_consumed as tokensConsumedLabel,
		days_consumed as daysConsumedLabel
	} from '$lib/paraglide/messages';
	import { formatDate } from '$lib/utils/date';
	import { onMount } from 'svelte';

	onMount(() => {
		// Track OnAi tab access
		analytics.trackFeatureAccess('onai_tab', true);
	});

	$: onAi = $onAiStore.data;
	$: loading_data = $onAiStore.loading;
	$: error = $onAiStore.error;

	let subscriptions: Record<string, SubscriptionResponse | null> = {};
	let subscriptionLoading: Record<string, boolean> = {};
	let subscriptionError: Record<string, string | null> = {};

	async function loadSubscription(onAiId: string) {
		subscriptionLoading = { ...subscriptionLoading, [onAiId]: true };
		try {
			const sub = await subscriptionService.getCurrent(onAiId);
			subscriptions = { ...subscriptions, [onAiId]: sub };
		} catch (err: any) {
			subscriptionError = {
				...subscriptionError,
				[onAiId]: err?.message ?? 'failed to load'
			};
		} finally {
			subscriptionLoading = { ...subscriptionLoading, [onAiId]: false };
		}
	}

	$: if (onAi && onAi.length) {
		onAi.forEach((instance) => {
			if (subscriptions[instance.id] === undefined && !subscriptionLoading[instance.id]) {
				loadSubscription(instance.id);
			}
		});
	}

	function getDayUsage(sub: SubscriptionResponse) {
		const start = new Date(sub.start_date);
		const end = new Date(sub.end_date);
		const now = new Date();
		const total = Math.max(1, Math.ceil((end.getTime() - start.getTime()) / 86400000));
		const remaining = Math.max(0, Math.ceil((end.getTime() - now.getTime()) / 86400000));
		const used = total - remaining;
		const percentage = Math.min(100, Math.max(0, (used / total) * 100));
		return { total, used, percentage };
	}

	function handleCreateNew() {
		// Track OnAi creation initiation
		analytics.trackFeatureAccess('onai_creation_dialog', true);

		// Original functionality here (if any)
	}

	function handleManageOnAi(onAiId: string, onAiName: string) {
		// Track OnAi management access
		analytics.trackFeatureAccess('onai_management', true);

		// Original functionality here (if any)
	}
</script>

<div class="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
	{#if loading_data}
		{#each Array(6) as _, i}
			<Card>
				<CardHeader>
					<CardTitle>
						<div class="flex items-center justify-between">
							<Skeleton class="h-5 w-[120px]" />
							<Skeleton class="h-4 w-[80px]" />
						</div>
					</CardTitle>
				</CardHeader>
				<CardContent>
					<div class="flex min-h-[120px] flex-col justify-between">
						<div class="flex justify-between text-sm">
							<Skeleton class="h-4 w-[80px]" />
							<Skeleton class="h-4 w-[40px]" />
						</div>
						<div>
							<Skeleton class="h-9 w-full" />
						</div>
					</div>
				</CardContent>
			</Card>
		{/each}
	{:else if error}
		<div class="col-span-full py-4 text-center text-red-500">
			<p>{error_generic()}: {error || ''}</p>
		</div>
	{:else}
		{#each onAi as instance, i}
			<a
				href={`/dashboard/on.ai/${instance.id}`}
				data-ph-capture-attribute-onai-id={instance.id}
				data-ph-capture-attribute-onai-name={instance.name || 'unnamed'}
				data-ph-capture-attribute-onai-active={instance.is_active}
				data-ph-capture-attribute-channels-count={instance.channels_count}
				data-ph-capture-attribute-action="onai-management-access"
			>
				<Card
					class="slide-up fade-in h-full cursor-pointer transition-all duration-300 hover:scale-[1.02] hover:shadow-lg"
					style="--animation-delay: {i * 100}ms;"
				>
					<CardHeader>
						<CardTitle>
							<div class="flex items-center justify-between">
								<span class="max-w-[70%] truncate">{instance.name || unnamed()}</span>
								<span class="text-muted-foreground shrink-0 text-xs"
									>{formatDate(new Date(instance.created_at))}</span
								>
							</div>
						</CardTitle>
					</CardHeader>
					<CardContent>
						<div class="flex min-h-[120px] flex-col justify-between gap-2">
							<div class="flex flex-col gap-2">
								<div class="flex justify-between text-sm">
									<span class="text-muted-foreground">{status()}:</span>
									<span
										class={instance.is_active
											? 'font-medium text-[#0058d6] dark:text-blue-400'
											: 'font-medium text-red-500 dark:text-red-400'}
									>
										{instance.is_active ? 'on.ai' : 'off'}
									</span>
								</div>

								<div class="flex justify-between text-sm">
									<span class="text-muted-foreground">{channels()}:</span>
									<span class="font-medium text-[#0058d6] dark:text-blue-400">
										{instance.channels_count}
									</span>
								</div>
							</div>

							{#if SUBSCRIPTION_ENABLED}
								{#if subscriptionLoading[instance.id]}
									<Skeleton class="mb-2 h-2 w-full" />
									<Skeleton class="mb-2 h-2 w-full" />
								{:else if subscriptions[instance.id]}
									{@const sub = subscriptions[instance.id]!}
									{#if sub.is_valid}
										<div class="mb-2">
											<p class="text-muted-foreground text-xs">{tokensConsumedLabel()}:</p>
											<p class="text-sm font-medium">
												{sub.tokens_used ?? sub.token_limit - sub.tokens_remaining} / {sub.token_limit}
											</p>
											<div class="relative mt-1 h-1 w-full rounded bg-gray-200">
												<div
													class={'absolute left-0 top-0 h-full rounded ' +
														((sub.usage_percentage ?? 0) > 80 ? 'bg-destructive' : 'bg-primary')}
													style="width: {sub.usage_percentage}%"
												></div>
											</div>
										</div>
										<div class="mb-2">
											<p class="text-muted-foreground text-xs">{daysConsumedLabel()}:</p>
											<p class="text-sm font-medium">
												{getDayUsage(sub).used} / {getDayUsage(sub).total}
											</p>
											<div class="relative mt-1 h-1 w-full rounded bg-gray-200">
												<div
													class={'absolute left-0 top-0 h-full rounded ' +
														(getDayUsage(sub).percentage > 80 ? 'bg-destructive' : 'bg-primary')}
													style="width: {getDayUsage(sub).percentage}%"
												></div>
											</div>
										</div>
									{:else if subscriptionError[instance.id]}
										<p class="text-destructive text-xs">{subscriptionError[instance.id]}</p>
									{:else}
										<div class="mb-2 text-center text-base text-green-700">
											{failed_to_load_subscription()}
										</div>
									{/if}
								{/if}
							{/if}

							<div>
								<button
									type="button"
									class={cn(buttonVariants({ variant: 'outline' }), 'w-full')}
									data-ph-capture-attribute-onai-id={instance.id}
									data-ph-capture-attribute-onai-name={instance.name || 'unnamed'}
									data-ph-capture-attribute-action="onai-management-button"
									onclick={() =>
										analytics.trackButtonClicked('onai_management', 'onai_tab', 'outline')}
								>
									<SettingsIcon class="mr-1 h-4 w-4" />
									{management()}
								</button>
							</div>
						</div>
					</CardContent>
				</Card>
			</a>
		{/each}
		<a
			href="/dashboard/on.ai/new"
			data-ph-capture-attribute-action="onai-creation"
			data-ph-capture-attribute-current-onai-count={onAi.length}
			onclick={() => analytics.trackButtonClicked('create_new_onai', 'onai_tab', 'primary')}
		>
			<Card
				class="slide-up fade-in h-full cursor-pointer transition-all duration-300 hover:scale-[1.02] hover:shadow-lg"
				style="--animation-delay: {onAi.length * 100}ms;"
			>
				<CardContent class="flex min-h-[220px] flex-col items-center justify-center">
					<tgs-player class="h-24 w-full" autoplay loop mode="normal" src="/animations/egg.tgs"
					></tgs-player>
					<p class="font-medium">{new_onai()}</p>
					<p class="text-muted-foreground mt-1 text-center text-xs">
						{new_instruction_question()}
					</p>
				</CardContent>
			</Card>
		</a>
	{/if}
</div>
