<script lang="ts">
	import * as RadioGroup from '$lib/components/ui/radio-group/index.js';
	import { Label } from '$lib/components/ui/label/index.js';
	import { type KnowledgeType } from '$lib/types/knowledge.types';
	import {
		knowledge_text,
		knowledge_document,
		knowledge_type_select
	} from '$lib/paraglide/messages';

	export let value: KnowledgeType;
	export let onChange: (type: KnowledgeType) => void;

	function handleValueChange(newValue: string | undefined) {
		if (newValue && (newValue === 'text' || newValue === 'document')) {
			onChange(newValue as KnowledgeType);
		}
	}
</script>

<div class="pb-2">
	<span class="mb-2 block text-sm font-medium">{knowledge_type_select()}</span>
	<RadioGroup.Root {value} onValueChange={handleValueChange} class="mb-2">
		<div class="flex items-center space-x-6">
			<div class="flex items-center space-x-2">
				<RadioGroup.Item
					value="text"
					id="type-text"
					data-ph-capture-attribute-knowledge-type="text"
					data-ph-capture-attribute-action="knowledge-type-selection"
				/>
				<Label for="type-text">{knowledge_text()}</Label>
			</div>
			<div class="flex items-center space-x-2">
				<RadioGroup.Item
					value="document"
					id="type-document"
					data-ph-capture-attribute-knowledge-type="document"
					data-ph-capture-attribute-action="knowledge-type-selection"
				/>
				<Label for="type-document">{knowledge_document()}</Label>
			</div>
		</div>
	</RadioGroup.Root>
</div>
