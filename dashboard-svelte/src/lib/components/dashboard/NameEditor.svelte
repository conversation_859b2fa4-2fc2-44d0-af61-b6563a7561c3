<script lang="ts">
	import { But<PERSON> } from '$lib/components/ui/button';
	import { Input } from '$lib/components/ui/input';
	import { CardTitle } from '$lib/components/ui/card';
	import { Pencil, Trash2, X, Check } from '@lucide/svelte';
	import { onAiStore } from '$lib/stores/onai-store';
	import { onAiUIStore } from '$lib/stores/on-ai-ui-store';
	import DeleteDialog from '$lib/components/dashboard/DeleteDialog.svelte';
	import * as Tooltip from '$lib/components/ui/tooltip';
	import { toast } from 'svelte-sonner';
	import {
		onai_name_updated,
		onai_name_update_failed,
		onai_name_placeholder,
		onai_name_edit,
		cancel,
		onai_name_save,
		onai_name_unnamed,
		onai_delete
	} from '$lib/paraglide/messages';
	import { analytics } from '$lib/services/analytics';

	let showDeleteDialog = false;
	$: editValue = $onAiStore.ui.editName;

	$: if (editValue !== $onAiStore.ui.editName) {
		onAiStore.updateEditName(editValue);
	}

	async function saveName() {
		analytics.trackButtonClicked('save_name', 'name_editor', 'primary');

		onAiUIStore.setIsUpdating(true);
		try {
			const result = await onAiStore.updateName(editValue);
			if (result.success) {
				analytics.trackFormSubmitted('onai_name_update', true);
				toast.success(onai_name_updated());
			} else {
				analytics.trackFormSubmitted('onai_name_update', false, [result.error || 'Unknown error']);
				analytics.trackErrorOccurred(
					'name_update_failed',
					result.error || 'Unknown error',
					'name_editor',
					'medium'
				);
				toast.error(result.error ?? onai_name_update_failed());
			}
		} catch (error) {
			analytics.trackFormSubmitted('onai_name_update', false, [String(error)]);
			analytics.trackErrorOccurred('name_update_exception', String(error), 'name_editor', 'high');
			console.error('Failed to update on.ai name:', error);
			toast.error(onai_name_update_failed());
		} finally {
			onAiUIStore.setIsUpdating(false);
		}
	}

	function handleKeyDown(e: KeyboardEvent) {
		if (e.key === 'Enter') {
			saveName();
		}
	}

	function handleStartEditing() {
		analytics.trackButtonClicked('start_edit_name', 'name_editor', 'ghost');
		onAiStore.startEditing();
	}

	function handleCancelEditing() {
		analytics.trackButtonClicked('cancel_edit_name', 'name_editor', 'outline');
		onAiStore.cancelEditing();
	}

	function handleShowDeleteDialog() {
		analytics.trackButtonClicked('show_delete_dialog', 'name_editor', 'destructive');
		showDeleteDialog = true;
	}
</script>

{#if $onAiStore.ui.isEditing}
	<div class="flex items-center gap-3">
		<Input
			type="text"
			bind:value={editValue}
			class="focus:border-primary focus:ring-primary/20 rounded-md border-2 px-3 py-1.5 text-lg font-semibold shadow-sm transition-colors focus:ring-2"
			onkeydown={handleKeyDown}
			placeholder={onai_name_placeholder()}
			autofocus
			disabled={$onAiUIStore.isUpdating}
		/>
		<div class="flex items-center gap-2">
			<Tooltip.Provider>
				<Tooltip.Root>
					<Tooltip.Trigger>
						<Button
							variant="outline"
							size="sm"
							onclick={handleCancelEditing}
							class="hover:bg-destructive/10 hover:text-destructive transition-colors"
							disabled={$onAiUIStore.isUpdating}
						>
							<X class="h-4 w-4" />
						</Button>
					</Tooltip.Trigger>
					<Tooltip.Content>
						<p>{cancel()}</p>
					</Tooltip.Content>
				</Tooltip.Root>
			</Tooltip.Provider>

			<Tooltip.Provider>
				<Tooltip.Root>
					<Tooltip.Trigger>
						<Button
							variant="default"
							size="sm"
							onclick={saveName}
							class="transition-opacity hover:opacity-90"
							disabled={$onAiUIStore.isUpdating}
						>
							<Check class="h-4 w-4" />
						</Button>
					</Tooltip.Trigger>
					<Tooltip.Content>
						<p>{onai_name_save()}</p>
					</Tooltip.Content>
				</Tooltip.Root>
			</Tooltip.Provider>
		</div>
	</div>
{:else}
	<div class="flex items-center gap-4">
		<CardTitle class="text-xl font-bold">{$onAiStore.api?.name || onai_name_unnamed()}</CardTitle>
		<div class="flex items-center gap-2">
			<Tooltip.Provider>
				<Tooltip.Root>
					<Tooltip.Trigger>
						<button
							class="hover:bg-accent/80 rounded-full p-2 transition-colors duration-200"
							onclick={handleStartEditing}
						>
							<Pencil class="text-muted-foreground h-4 w-4" />
						</button>
					</Tooltip.Trigger>
					<Tooltip.Content>
						<p>{onai_name_edit()}</p>
					</Tooltip.Content>
				</Tooltip.Root>
			</Tooltip.Provider>

			<Tooltip.Provider>
				<Tooltip.Root>
					<Tooltip.Trigger>
						<button
							class="rounded-full p-2 transition-colors duration-200 hover:bg-red-100"
							onclick={handleShowDeleteDialog}
						>
							<Trash2 class="h-4 w-4 text-red-500" />
						</button>
					</Tooltip.Trigger>
					<Tooltip.Content>
						<p>{onai_delete()}</p>
					</Tooltip.Content>
				</Tooltip.Root>
			</Tooltip.Provider>
		</div>
	</div>
{/if}

<DeleteDialog bind:open={showDeleteDialog} />
