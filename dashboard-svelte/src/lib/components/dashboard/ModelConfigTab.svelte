<script lang="ts">
	import { toast } from 'svelte-sonner';
	import { Slider } from '$lib/components/ui/slider';
	import { onMount, onDestroy } from 'svelte';
	import { onAiService } from '$lib/services/on-ai';
	import { debounce } from 'es-toolkit';
	import {
		model_temperature,
		model_temperature_description,
		model_temperature_updated,
		model_temperature_update_failed,
		model_temperature_load_failed
	} from '$lib/paraglide/messages';

	const { onAiId } = $props<{ onAiId: string }>();
	let temperature = $state(0.7);
	let initialTemperature = $state(0.7);

	onMount(async () => {
		try {
			const response = await onAiService.getModelTemperature(onAiId);
			temperature = response.temperature;
			initialTemperature = response.temperature;
		} catch (error) {
			toast.error(model_temperature_load_failed());
		}
	});

	const debouncedUpdate = debounce(async (value: number) => {
		try {
			await onAiService.updateModelTemperature(onAiId, value);
			toast.success(model_temperature_updated());
		} catch (error) {
			toast.error(model_temperature_update_failed());
		}
	}, 500);

	onDestroy(() => {
		debouncedUpdate.cancel();
	});

	$effect(() => {
		if (temperature !== initialTemperature) {
			debouncedUpdate(temperature);
		}
	});
</script>

<div class="space-y-6 md:space-y-8">
	<div class="w-full">
		<h2 class="mb-4 lowercase">{model_temperature()}</h2>
		<p class="text-muted-foreground mb-4 text-sm lowercase">{model_temperature_description()}</p>
		<div class="w-full max-w-sm">
			<Slider type="single" bind:value={temperature} max={1} min={0} step={0.1} />
			<p class="text-muted-foreground mt-2 text-sm">{temperature.toFixed(1)}</p>
		</div>
	</div>
</div>
