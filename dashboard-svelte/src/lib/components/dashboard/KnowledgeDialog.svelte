<script lang="ts">
	import * as Dialog from '$lib/components/ui/dialog';
	import * as Drawer from '$lib/components/ui/drawer';
	import { useResponsive } from '$lib/utils/responsive';
	import type { KnowledgeType } from '$lib/types/knowledge.types';
	import KnowledgeForm from './KnowledgeForm.svelte';
	import { knowledge_upload_document, knowledge_upload_text } from '$lib/paraglide/messages';
	import { analytics } from '$lib/services/analytics';
	import { onMount } from 'svelte';

	export let open = false;
	export let type: KnowledgeType = 'text';
	export let title: string;
	export let initialName: string = '';

	const { isMobile } = useResponsive();
	$: isDesktop = !$isMobile;

	let openStartTime: number;

	$: if (open) {
		openStartTime = Date.now();
		analytics.trackModalOpened('knowledge_dialog', type);
	}

	function handleClose() {
		if (openStartTime) {
			const duration = Date.now() - openStartTime;
			analytics.trackModalClosed('knowledge_dialog', 'close', duration);
		}
		open = false;
	}

	function getDialogDescription() {
		return type === 'document' ? knowledge_upload_document() : knowledge_upload_text();
	}
</script>

{#if isDesktop}
	<Dialog.Root bind:open>
		<Dialog.Content class="m-0 flex h-screen w-screen max-w-none flex-col rounded-none p-6">
			<div class="absolute right-4 top-4">
				<Dialog.Close />
			</div>
			<Dialog.Header class="pb-2">
				<Dialog.Title>{title}</Dialog.Title>
				<Dialog.Description>
					{getDialogDescription()}
				</Dialog.Description>
			</Dialog.Header>
			<div class="flex-1 overflow-hidden">
				{#if open}
					<KnowledgeForm bind:type {initialName} {open} onClose={handleClose} />
				{/if}
			</div>
		</Dialog.Content>
	</Dialog.Root>
{:else}
	<Drawer.Root bind:open>
		<Drawer.Content class="flex h-screen flex-col">
			<Drawer.Header class="text-left">
				<Drawer.Title>{title}</Drawer.Title>
				<Drawer.Description>
					{getDialogDescription()}
				</Drawer.Description>
			</Drawer.Header>
			<div class="flex-1 overflow-hidden px-4">
				{#if open}
					<KnowledgeForm bind:type {initialName} {open} onClose={handleClose} />
				{/if}
			</div>
		</Drawer.Content>
	</Drawer.Root>
{/if}
