<script lang="ts">
	import { Button } from '$lib/components/ui/button';
	import PromptDialog from '$lib/components/dashboard/PromptDialog.svelte';
	import PromptMissingWarning from '$lib/components/dashboard/PromptMissingWarning.svelte';
	import { onAiUIStore, isPromptMissing } from '$lib/stores/on-ai-ui-store';
	import { promptStore } from '$lib/stores/prompt-store';
	import { PROMPT_MAX_CHARS } from '$lib/constants/prompt';
	import * as logsAPI from '@opentelemetry/api-logs';
	import { SERVICE_NAME } from '$lib/constants/telemetry';

	const logger = logsAPI.logs.getLogger(SERVICE_NAME);

	import {
		onai_instructions,
		onai_base,
		onai_reminder,
		onai_report,
		onai_edit_base,
		onai_update_base,
		onai_base_updated,
		onai_enter_base,
		onai_edit_reminder,
		onai_update_reminder,
		onai_reminder_updated,
		onai_enter_reminder,
		onai_edit_report,
		onai_update_report,
		onai_report_updated,
		onai_enter_report
	} from '$lib/paraglide/messages';

	export let formError: string | undefined;

	function handleMainPromptDialogVisibility(visible: boolean) {
		if (visible) {
			onAiUIStore.setShowTestPromptDialog(false);
			onAiUIStore.setShowReportPromptDialog(false);
		}
		onAiUIStore.setShowMainPromptDialog(visible);
		if (visible) {
			logger.emit({
				body: `Main prompt dialog opened with data: ${$promptStore.main.content?.length}`,
				severityNumber: logsAPI.SeverityNumber.INFO,
				severityText: 'INFO'
			});
		} else {
			logger.emit({
				body: 'Main prompt dialog closed',
				severityNumber: logsAPI.SeverityNumber.INFO,
				severityText: 'INFO'
			});
		}
	}

	function handleReportPromptDialogVisibility(visible: boolean) {
		if (visible) {
			onAiUIStore.setShowMainPromptDialog(false);
			onAiUIStore.setShowTestPromptDialog(false);
		}
		onAiUIStore.setShowReportPromptDialog(visible);
		if (visible) {
			logger.emit({
				body: `Report prompt dialog opened with data: ${$promptStore.report.content?.length}`,
				severityNumber: logsAPI.SeverityNumber.INFO,
				severityText: 'INFO'
			});
		} else {
			logger.emit({
				body: 'Report prompt dialog closed',
				severityNumber: logsAPI.SeverityNumber.INFO,
				severityText: 'INFO'
			});
		}
	}

	function showMainPrompt() {
		handleMainPromptDialogVisibility(true);
	}

	function showReportPrompt() {
		handleReportPromptDialogVisibility(true);
	}
</script>

<div class="w-full">
	<h2 class="mb-4 lowercase">{onai_instructions()}</h2>

	{#if $isPromptMissing}
		<div class="mb-4">
			<PromptMissingWarning />
		</div>
	{/if}

	<div class="flex flex-wrap gap-2">
		<Button variant="default" onclick={showMainPrompt}>
			{onai_base()}
		</Button>
		<Button variant="default" onclick={showReportPrompt}>
			{onai_report()}
		</Button>
	</div>

	<PromptDialog
		open={$onAiUIStore.showMainPromptDialog}
		on:open={(e) => handleMainPromptDialogVisibility(e.detail)}
		type="main"
		title={onai_edit_base()}
		description={onai_update_base()}
		label={onai_base()}
		placeholder={onai_enter_base()}
		maxLength={PROMPT_MAX_CHARS}
		successMessage={onai_base_updated()}
		{formError}
		store={promptStore}
		on:close={() => handleMainPromptDialogVisibility(false)}
	/>

	<PromptDialog
		open={$onAiUIStore.showReportPromptDialog}
		on:open={(e) => handleReportPromptDialogVisibility(e.detail)}
		type="report"
		title={onai_edit_report()}
		description={onai_update_report()}
		label={onai_report()}
		placeholder={onai_enter_report()}
		maxLength={PROMPT_MAX_CHARS}
		successMessage={onai_report_updated()}
		{formError}
		store={promptStore}
		on:close={() => handleReportPromptDialogVisibility(false)}
	/>
</div>
