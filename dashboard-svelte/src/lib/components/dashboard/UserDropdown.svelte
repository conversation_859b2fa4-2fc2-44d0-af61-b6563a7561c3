<script lang="ts">
	import { User, LogOut, Settings } from '@lucide/svelte';
	import { buttonVariants } from '$lib/components/ui/button';
	import * as DropdownMenu from '$lib/components/ui/dropdown-menu/index.js';
	import { cn } from '$lib/utils';
	import { auth } from '$lib/stores/auth';
	import { logout, settings } from '$lib/paraglide/messages';
	import { IdentificationSource } from '$lib/types/auth.types';
	import { formatPhoneNumberSafely } from '$lib/utils/phone';

	let contact = $derived(
		$auth.user?.primary_source === IdentificationSource.PHONE && $auth.user?.phone_number
			? formatPhoneNumberSafely($auth.user.phone_number)
			: $auth.user?.email || ''
	);
</script>

<DropdownMenu.Root>
	<DropdownMenu.Trigger
		class={cn(buttonVariants({ variant: 'outline' }))}
		data-ph-capture-attribute-action="user-menu-dropdown"
		data-ph-capture-attribute-user-contact={contact}
	>
		<span class="text-sm">{contact}</span>
		<User class="ml-1 h-4 w-4" />
	</DropdownMenu.Trigger>
	<DropdownMenu.Content class="w-[var(--radix-dropdown-menu-trigger-width)] min-w-0">
		<DropdownMenu.Item>
			<a
				href="/dashboard/settings"
				class="flex items-center"
				data-ph-capture-attribute-action="header-settings-click"
			>
				<Settings class="mr-2 h-4 w-4" />
				<span>{settings()}</span>
			</a>
		</DropdownMenu.Item>
		<DropdownMenu.Separator />
		<DropdownMenu.Item class="text-destructive">
			<a
				href="/logout"
				class="flex items-center"
				data-ph-capture-attribute-action="header-logout-click"
			>
				<LogOut class="mr-2 h-4 w-4" />
				<span>{logout()}</span>
			</a>
		</DropdownMenu.Item>
	</DropdownMenu.Content>
</DropdownMenu.Root>
