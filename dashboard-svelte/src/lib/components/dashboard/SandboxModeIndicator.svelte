<script lang="ts">
	import { Badge } from '$lib/components/ui/badge';
	import { chatFeaturesStore } from '$lib/stores/chat-features-store';
	import { sandbox_mode } from '$lib/paraglide/messages';
	import MaterialSymbolsScience from '~icons/material-symbols/science';
	import ChatFeaturesDialog from './ChatFeaturesDialog.svelte';

	export let onAiId: string;
	export let visible: boolean = true;

	let showChatFeaturesDialog = false;

	// Load sandbox mode data when component mounts
	$: if (onAiId && visible) {
		chatFeaturesStore.load(onAiId);
	}

	function handleClick() {
		showChatFeaturesDialog = true;
	}
</script>

{#if visible && $chatFeaturesStore.sandbox_mode_enabled}
	<Badge
		variant="warning"
		class="flex cursor-pointer items-center gap-1 text-xs transition-opacity hover:opacity-80"
		onclick={handleClick}
	>
		<MaterialSymbolsScience class="h-3 w-3" />
		{sandbox_mode()}
	</Badge>
{/if}

{#if showChatFeaturesDialog}
	<ChatFeaturesDialog bind:open={showChatFeaturesDialog} {onAiId} />
{/if}
