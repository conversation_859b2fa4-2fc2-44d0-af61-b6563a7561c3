<script lang="ts">
	import { Button } from '$lib/components/ui/button';
	import { formatDate } from '$lib/utils/date';
	import { type KnowledgeVersion } from '$lib/types/knowledge.types';
	import { knowledgeStore } from '$lib/stores/knowledge-store';
	import type { KnowledgeType } from '$lib/types/knowledge.types';
	import { onAiStore } from '$lib/stores/onai-store';
	import { Skeleton } from '$lib/components/ui/skeleton';
	import { history_empty, knowledge_restore, close, no_description } from '$lib/paraglide/messages';
	import { analytics } from '$lib/services/analytics';

	export let type: KnowledgeType;
	export let knowledgeId: string;
	export let onRestore: (content: string) => void;
	export let onClose: () => void;
	export let open = false;

	let versions: KnowledgeVersion[] = [];
	let isLoading = false;

	$: if (open && $onAiStore.api?.id && knowledgeId) {
		analytics.trackModalOpened('knowledge_history', 'version_management');
		loadVersions();
	}

	async function loadVersions() {
		analytics.trackFeatureAccess('knowledge_history', true);
		isLoading = true;
		try {
			versions = (await knowledgeStore.loadVersions(type, knowledgeId))?.reverse() || [];
			analytics.trackPerformanceMetric(
				'knowledge_versions_loaded',
				versions.length,
				'knowledge_history'
			);
		} catch (error) {
			analytics.trackErrorOccurred(
				'knowledge_versions_load_failed',
				String(error),
				'knowledge_history',
				'medium'
			);
		} finally {
			isLoading = false;
		}
	}

	async function handleRestore(version: KnowledgeVersion) {
		analytics.trackButtonClicked('restore_knowledge_version', 'knowledge_history', 'primary');

		try {
			const content = await knowledgeStore.getVersionContent(
				type,
				knowledgeId,
				version.version_number
			);
			if (content) {
				analytics.trackFormSubmitted('restore_knowledge_version', true);
				onRestore(content);
				onClose();
			} else {
				analytics.trackFormSubmitted('restore_knowledge_version', false, ['No content retrieved']);
			}
		} catch (error) {
			analytics.trackFormSubmitted('restore_knowledge_version', false);
			analytics.trackErrorOccurred(
				'knowledge_restore_failed',
				String(error),
				'knowledge_history',
				'medium'
			);
		}
	}
</script>

<div class="max-h-[60vh] overflow-y-auto">
	{#if isLoading}
		{#each Array(3) as _}
			<div class="mb-4 rounded-lg border p-4">
				<div class="mb-2 flex items-center justify-between">
					<Skeleton class="h-4 w-[100px]" />
					<div class="flex gap-2">
						<Skeleton class="h-8 w-[100px]" />
					</div>
				</div>
				<Skeleton class="h-4 w-full" />
			</div>
		{/each}
	{:else if versions.length === 0}
		<p class="text-center text-sm text-gray-500">{history_empty()}</p>
	{:else}
		{#each versions as version}
			<div class="mb-4 rounded-lg border p-4">
				<div class="mb-2 flex items-center justify-between">
					<span class="text-sm text-gray-500">
						{formatDate(new Date(version.created_at))}
					</span>
					<div class="flex gap-2">
						<Button variant="outline" size="sm" onclick={() => handleRestore(version)}>
							{knowledge_restore()}
						</Button>
					</div>
				</div>
				<p class="text-sm text-gray-500">{version.change_description || no_description()}</p>
			</div>
		{/each}
	{/if}
</div>

<div class="flex justify-end">
	<Button variant="outline" onclick={onClose}>{close()}</Button>
</div>
