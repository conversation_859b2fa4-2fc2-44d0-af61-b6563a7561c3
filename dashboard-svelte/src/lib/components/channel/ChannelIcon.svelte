<script lang="ts">
	import { getChannelIcon } from '$lib/utils/channel';
	import { TransporterType } from '$lib/types/channel.types';

	export let type: TransporterType;
	export let size: 'sm' | 'md' | 'lg' = 'md';
	export let containerClass = '';

	const sizeClasses = {
		sm: 'h-4 w-4',
		md: 'h-6 w-6',
		lg: 'h-10 w-10'
	};

	$: iconClass = sizeClasses[size];

	$: iconComponent = getChannelIcon(type);
</script>

<div class={`flex items-center justify-center ${containerClass}`}>
	{#if typeof iconComponent === 'string'}
		<img src={iconComponent} alt="{type} icon" class={iconClass} />
	{:else if iconComponent}
		<svelte:component this={iconComponent} class={iconClass} />
	{/if}
</div>
