<script lang="ts">
	import { onMount, afterUpdate } from 'svelte';
	import {
		analytics_tokens_used_label,
		analytics_estimated_capacity,
		analytics_no_data_available
	} from '$lib/paraglide/messages';
	import { Chart } from '$lib/utils/chart-config';
	import { formatNumber } from '$lib/utils/date';
	import type { SubscriptionResponse } from '$lib/types/subscription.types';

	export let subscriptionData: SubscriptionResponse | null = null;

	let tokenUsageChart: Chart;
	let tokenUsageChartCanvas: HTMLCanvasElement;

	function createTokenUsageChart() {
		if (tokenUsageChartCanvas && subscriptionData) {
			const used = subscriptionData.tokens_used ?? 0;
			const remaining = Math.max(0, (subscriptionData.token_limit ?? 0) - used);
			if ((subscriptionData.token_limit ?? 0) === 0) return;

			const data = {
				labels: [analytics_tokens_used_label(), analytics_estimated_capacity()],
				datasets: [
					{
						data: [used, remaining],
						backgroundColor: ['rgba(54, 162, 235, 0.6)', 'rgba(201, 203, 207, 0.6)'],
						borderColor: ['rgba(54, 162, 235, 1)', 'rgba(201, 203, 207, 1)'],
						borderWidth: 1
					}
				]
			};

			if (tokenUsageChart) tokenUsageChart.destroy();

			tokenUsageChart = new Chart(tokenUsageChartCanvas, {
				type: 'doughnut',
				data,
				options: {
					responsive: true,
					plugins: {
						legend: {
							position: 'bottom'
						},
						title: {
							display: true,
							text: analytics_estimated_capacity()
						}
					}
				}
			});
		}
	}

	afterUpdate(() => {
		if (subscriptionData && subscriptionData.tokens_used !== undefined) {
			createTokenUsageChart();
		}
	});

	onMount(() => {
		return () => {
			if (tokenUsageChart) tokenUsageChart.destroy();
		};
	});
</script>

<div class="bg-background rounded-lg p-4 shadow-sm">
	{#if subscriptionData && subscriptionData.tokens_used !== undefined}
		<div class="flex h-64 items-center justify-center">
			<canvas bind:this={tokenUsageChartCanvas}></canvas>
		</div>
		<div class="mt-4 text-center">
			<p class="text-2xl font-bold">{formatNumber(subscriptionData.tokens_used ?? 0)}</p>
			<p class="text-muted-foreground text-sm">{analytics_tokens_used_label()}</p>
		</div>
	{:else}
		<div class="flex h-64 items-center justify-center">
			<p class="text-muted-foreground">{analytics_no_data_available()}</p>
		</div>
	{/if}
</div>
