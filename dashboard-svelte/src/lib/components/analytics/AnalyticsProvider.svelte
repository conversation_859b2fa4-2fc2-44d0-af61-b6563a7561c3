<script lang="ts">
	import { onMount, onDestroy } from 'svelte';
	import { browser } from '$app/environment';
	import { PUBLIC_POSTHOG_API_KEY, PUBLIC_POSTHOG_HOST } from '$env/static/public';
	import { analytics } from '$lib/services/analytics';

	onMount(() => {
		if (browser) {
			analytics.init(PUBLIC_POSTHOG_API_KEY, PUBLIC_POSTHOG_HOST);
		}
	});

	onDestroy(() => {
		if (browser) {
			analytics.stopRecording();
		}
	});
</script>
