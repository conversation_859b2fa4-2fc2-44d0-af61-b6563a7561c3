<script lang="ts">
	import { onMount, afterUpdate } from 'svelte';
	import {
		analytics_messages_by_role_title,
		analytics_role_assistant,
		analytics_role_user,
		analytics_role_from_me,
		analytics_total_messages_sent,
		analytics_no_data_available
	} from '$lib/paraglide/messages';
	import { Chart } from '$lib/utils/chart-config';
	import { formatNumber } from '$lib/utils/date';

	export let messageCountsByRole: Record<string, number> = {};
	export let totalMessages: number = 0;

	let messagesByRoleChartCanvas: HTMLCanvasElement;
	let messagesByRoleChart: Chart;

	function createMessagesByRoleChart() {
		if (messagesByRoleChartCanvas && Object.keys(messageCountsByRole).length) {
			const roles = Object.keys(messageCountsByRole);
			const counts = Object.values(messageCountsByRole);

			const backgroundColors = [
				'rgba(75, 192, 192, 0.6)',
				'rgba(54, 162, 235, 0.6)',
				'rgba(255, 159, 64, 0.6)'
			];
			const borderColors = [
				'rgba(75, 192, 192, 1)',
				'rgba(54, 162, 235, 1)',
				'rgba(255, 159, 64, 1)'
			];

			const translatedLabels = roles.map((role) => {
				if (role === 'assistant') return analytics_role_assistant();
				if (role === 'user') return analytics_role_user();
				if (role === 'from_me') return analytics_role_from_me();
				return role;
			});

			if (messagesByRoleChart) messagesByRoleChart.destroy();

			messagesByRoleChart = new Chart(messagesByRoleChartCanvas, {
				type: 'doughnut',
				data: {
					labels: translatedLabels,
					datasets: [
						{
							data: counts,
							backgroundColor: backgroundColors,
							borderColor: borderColors,
							borderWidth: 1
						}
					]
				},
				options: {
					responsive: true,
					plugins: {
						legend: {
							position: 'bottom'
						},
						title: {
							display: true,
							text: analytics_messages_by_role_title()
						}
					}
				}
			});
		}
	}

	afterUpdate(() => {
		if (Object.keys(messageCountsByRole).length > 0) {
			createMessagesByRoleChart();
		}
	});

	onMount(() => {
		return () => {
			if (messagesByRoleChart) messagesByRoleChart.destroy();
		};
	});
</script>

<div class="bg-background rounded-lg p-4 shadow-sm">
	{#if Object.keys(messageCountsByRole).length > 0}
		<div class="flex h-64 items-center justify-center">
			<canvas bind:this={messagesByRoleChartCanvas}></canvas>
		</div>
		<div class="mt-4 text-center">
			<p class="text-2xl font-bold">{formatNumber(totalMessages)}</p>
			<p class="text-muted-foreground text-sm">{analytics_total_messages_sent()}</p>
		</div>
	{:else}
		<div class="flex h-64 items-center justify-center">
			<p class="text-muted-foreground">{analytics_no_data_available()}</p>
		</div>
	{/if}
</div>
