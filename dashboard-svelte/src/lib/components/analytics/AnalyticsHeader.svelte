<script lang="ts">
	import {
		analytics_dashboard_title,
		analytics_timeframe,
		analytics_timeframe_all,
		analytics_timeframe_month,
		analytics_timeframe_week,
		select_channel,
		analytics_all_channels
	} from '$lib/paraglide/messages';
	import * as Select from '$lib/components/ui/select';
	import type { ChannelResponse } from '$lib/types/channel.types';
	import { analytics } from '$lib/services/analytics';

	export let timeframe: string = 'all';
	export let channels: ChannelResponse[] = [];
	export let selectedChannelId: string | undefined;
	export let onTimeframeChange: (timeframe: string) => void;
	export let onChannelChange: (channelId: string | undefined) => void;

	function getChannelLabel(id: string | undefined): string {
		if (!id) return analytics_all_channels();
		const ch = channels.find((c) => c.id === id);
		return (ch?.name ?? ch?.transporter_type ?? '').toLowerCase();
	}

	function handleTimeframeClick(newTimeframe: string) {
		analytics.trackFilterApplied('timeframe', newTimeframe, 'analytics_tab');
		onTimeframeChange(newTimeframe);
	}

	$: if (selectedChannelId !== undefined) {
		const channelLabel = selectedChannelId ? getChannelLabel(selectedChannelId) : 'all_channels';
		analytics.trackFilterApplied('channel', channelLabel, 'analytics_tab');
		onChannelChange(selectedChannelId);
	}
</script>

<div class="flex flex-col items-start justify-between gap-4 sm:flex-row sm:items-center">
	<h2 class="text-primary text-2xl font-semibold">{analytics_dashboard_title()}</h2>

	<div class="flex flex-col gap-2 sm:flex-row sm:items-center sm:space-x-4">
		<div class="flex items-center space-x-2">
			<span class="text-muted-foreground text-sm">{analytics_timeframe()}</span>
			<div class="bg-muted flex overflow-hidden rounded-md">
				<button
					class="px-3 py-1 text-sm {timeframe === 'all'
						? 'bg-primary text-primary-foreground'
						: 'hover:bg-muted-foreground/10'}"
					onclick={() => handleTimeframeClick('all')}>{analytics_timeframe_all()}</button
				>
				<button
					class="px-3 py-1 text-sm {timeframe === 'month'
						? 'bg-primary text-primary-foreground'
						: 'hover:bg-muted-foreground/10'}"
					onclick={() => handleTimeframeClick('month')}>{analytics_timeframe_month()}</button
				>
				<button
					class="px-3 py-1 text-sm {timeframe === 'week'
						? 'bg-primary text-primary-foreground'
						: 'hover:bg-muted-foreground/10'}"
					onclick={() => handleTimeframeClick('week')}>{analytics_timeframe_week()}</button
				>
			</div>
		</div>

		<div class="w-full sm:w-auto sm:min-w-[180px]">
			<Select.Root disabled={channels.length === 0} type="single" bind:value={selectedChannelId}>
				<Select.Trigger class="w-full">
					{selectedChannelId ? getChannelLabel(selectedChannelId) : select_channel()}
				</Select.Trigger>
				<Select.Content>
					<Select.Item value="">{analytics_all_channels()}</Select.Item>
					{#each channels as ch}
						<Select.Item value={ch.id}
							>{(ch.name ?? ch.transporter_type ?? ch.id ?? '').toLowerCase()}</Select.Item
						>
					{/each}
				</Select.Content>
			</Select.Root>
		</div>
	</div>
</div>
