<script lang="ts">
	import {
		analytics_user_header,
		analytics_tokens_header,
		analytics_cost_header,
		analytics_no_data_available
	} from '$lib/paraglide/messages';
	import { formatNumber } from '$lib/utils/date';

	export let topUsers: Array<{ user_id: string; tokens_used: number; cost_estimate: number }> = [];
	export let getCurrencySymbol: () => string;
</script>

<div class="bg-background rounded-lg p-4 shadow-sm">
	{#if topUsers.length > 0}
		<div class="overflow-x-auto">
			<table class="w-full border-collapse text-left">
				<thead>
					<tr class="border-b">
						<th class="px-4 py-2 font-medium">{analytics_user_header()}</th>
						<th class="px-4 py-2 font-medium">{analytics_tokens_header()}</th>
						<th class="px-4 py-2 font-medium"
							>{analytics_cost_header({ currency: getCurrencySymbol() })}</th
						>
					</tr>
				</thead>
				<tbody>
					{#each topUsers as u}
						<tr class="hover:bg-muted/50 border-b">
							<td class="px-4 py-2">{u.user_id}</td>
							<td class="px-4 py-2">{formatNumber(u.tokens_used)}</td>
							<td class="px-4 py-2">${u.cost_estimate.toFixed(2)}</td>
						</tr>
					{/each}
				</tbody>
			</table>
		</div>
	{:else}
		<div class="flex h-64 items-center justify-center">
			<p class="text-muted-foreground">{analytics_no_data_available()}</p>
		</div>
	{/if}
</div>
