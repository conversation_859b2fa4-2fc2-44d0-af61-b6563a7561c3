<script lang="ts">
	import { onMount, afterUpdate } from 'svelte';
	import { Chart } from '$lib/utils/chart-config';

	let tokenStatsChart: Chart;
	let tokenStatsChartCanvas: HTMLCanvasElement;
	let showPercentage = false;
	let timePeriod = 'daily';
	let tokenStatsLabels: string[] = [];
	let tokenStatsDatasets: any[] = [];

	function generateTokenStatsData(days = 80) {
		const labels = [];
		const startDate = new Date('2024-08-12');

		for (let i = 0; i < days; i++) {
			const date = new Date(startDate);
			date.setDate(date.getDate() + i);

			if (i % 7 === 0 || i === days - 1) {
				labels.push(
					date.toLocaleDateString('en-US', {
						month: 'short',
						day: 'numeric'
					})
				);
			} else {
				labels.push('');
			}
		}

		return labels;
	}

	function createTokenStatsDatasets(labels: string[]) {
		return [
			{
				label: 'input tokens',
				data: labels.map((_, i) => {
					const baseUsage = 8000 + Math.sin(i * 0.1) * 2000;
					const weeklyPattern = Math.sin(i * 0.02) * 1500;
					const growth = i * 50;
					const randomVariation = (Math.random() - 0.5) * 1000;
					return Math.max(1000, baseUsage + weeklyPattern + growth + randomVariation);
				}),
				backgroundColor: '#00d4ff',
				borderWidth: 0,
				barPercentage: 0.8,
				categoryPercentage: 0.9
			},
			{
				label: 'output tokens',
				data: labels.map((_, i) => {
					const baseUsage = 12000 + Math.sin(i * 0.12) * 3000;
					const weeklyPattern = Math.sin(i * 0.025) * 2000;
					const growth = i * 80;
					const randomVariation = (Math.random() - 0.5) * 1500;
					return Math.max(1500, baseUsage + weeklyPattern + growth + randomVariation);
				}),
				backgroundColor: '#ff6b9d',
				borderWidth: 0,
				barPercentage: 0.8,
				categoryPercentage: 0.9
			},
			{
				label: 'vision tokens',
				data: labels.map((_, i) => {
					const baseUsage = 400 + Math.sin(i * 0.2) * 150;
					const growth = i * 4;
					const randomVariation = (Math.random() - 0.5) * 100;
					return Math.max(20, baseUsage + growth + randomVariation);
				}),
				backgroundColor: '#ff8a50',
				borderWidth: 0,
				barPercentage: 0.8,
				categoryPercentage: 0.9
			}
		];
	}

	function createTokenStatsChart() {
		if (tokenStatsChartCanvas) {
			if (tokenStatsChart) tokenStatsChart.destroy();

			tokenStatsChart = new Chart(tokenStatsChartCanvas, {
				type: 'bar',
				data: {
					labels: tokenStatsLabels,
					datasets: tokenStatsDatasets
				},
				options: {
					responsive: true,
					maintainAspectRatio: false,
					interaction: {
						mode: 'index',
						intersect: false
					},
					plugins: {
						legend: {
							display: false
						},
						tooltip: {
							mode: 'index',
							intersect: false,
							backgroundColor: 'rgba(44, 62, 80, 0.95)',
							titleColor: '#ecf0f1',
							bodyColor: '#ecf0f1',
							borderColor: '#34495e',
							borderWidth: 1,
							cornerRadius: 8,
							titleFont: { size: 14, weight: 'bold' },
							bodyFont: { size: 12 },
							callbacks: {
								title: function (context: any) {
									const date = new Date('2024-08-12');
									date.setDate(date.getDate() + context[0].dataIndex);
									return date.toLocaleDateString('en-US', {
										weekday: 'short',
										month: 'short',
										day: 'numeric',
										year: 'numeric'
									});
								},
								label: function (context: any) {
									const value = context.parsed.y.toLocaleString();
									return `${context.dataset.label}: ${value} tokens`;
								},
								footer: function (context: any) {
									const total = context.reduce((sum: number, item: any) => sum + item.parsed.y, 0);
									return `total: ${total.toLocaleString()} tokens`;
								}
							}
						}
					},
					scales: {
						x: {
							stacked: true,
							display: true,
							grid: {
								display: false
							},
							ticks: {
								maxRotation: 0,
								color: '#7f8c8d',
								font: {
									size: 11,
									weight: 500
								}
							}
						},
						y: {
							stacked: true,
							display: true,
							position: 'left',
							grid: {
								color: '#ecf0f1',
								lineWidth: 1
							},
							ticks: {
								color: '#7f8c8d',
								font: {
									size: 11,
									weight: 500
								},
								callback: function (value: any) {
									if (value >= 1000000) return (value / 1000000).toFixed(1) + 'M';
									if (value >= 1000) return (value / 1000).toFixed(0) + 'K';
									return value.toLocaleString();
								}
							},
							beginAtZero: true
						}
					}
				}
			});
		}
	}

	function initializeTokenStatsData() {
		let days = 80;
		switch (timePeriod) {
			case 'weekly':
				days = 12;
				break;
			case 'monthly':
				days = 6;
				break;
			default:
				days = 80;
		}

		tokenStatsLabels = generateTokenStatsData(days);
		tokenStatsDatasets = createTokenStatsDatasets(tokenStatsLabels);
	}

	function handleRegenerateData() {
		tokenStatsDatasets.forEach((dataset, index) => {
			dataset.data = tokenStatsLabels.map((_, i) => {
				const multiplier = [1, 1.5, 0.05][index] || 0.1;
				const baseUsage = (5000 + index * 2000) * multiplier;
				const variation = Math.sin(i * 0.1 + index) * baseUsage * 0.3;
				const growth = i * (20 + index * 10);
				const randomVariation = (Math.random() - 0.5) * baseUsage * 0.2;
				return Math.max(100, baseUsage + variation + growth + randomVariation);
			});
		});
		if (tokenStatsChart) {
			tokenStatsChart.update('active');
		}
	}

	function handleToggleView() {
		showPercentage = !showPercentage;

		if (showPercentage) {
			const newData = tokenStatsLabels.map((_, dayIndex) => {
				const dayTotal = tokenStatsDatasets.reduce(
					(sum, dataset) => sum + dataset.data[dayIndex],
					0
				);
				return tokenStatsDatasets.map((dataset) => (dataset.data[dayIndex] / dayTotal) * 100);
			});

			tokenStatsDatasets.forEach((dataset, datasetIndex) => {
				dataset.data = newData.map((day) => day[datasetIndex]);
			});

			if (
				tokenStatsChart &&
				tokenStatsChart.options?.scales?.y?.ticks &&
				tokenStatsChart.options?.plugins?.tooltip?.callbacks
			) {
				tokenStatsChart.options.scales.y.ticks.callback = function (value: any) {
					return value.toFixed(1) + '%';
				};
				tokenStatsChart.options.plugins.tooltip.callbacks.label = function (context: any) {
					return `${context.dataset.label}: ${context.parsed.y.toFixed(1)}%`;
				};
			}
		} else {
			handleRegenerateData();
			if (
				tokenStatsChart &&
				tokenStatsChart.options?.scales?.y?.ticks &&
				tokenStatsChart.options?.plugins?.tooltip?.callbacks
			) {
				tokenStatsChart.options.scales.y.ticks.callback = function (value: any) {
					if (value >= 1000000) return (value / 1000000).toFixed(1) + 'M';
					if (value >= 1000) return (value / 1000).toFixed(0) + 'K';
					return value.toLocaleString();
				};
				tokenStatsChart.options.plugins.tooltip.callbacks.label = function (context: any) {
					const value = context.parsed.y.toLocaleString();
					return `${context.dataset.label}: ${value} tokens`;
				};
			}
		}
		if (tokenStatsChart) {
			tokenStatsChart.update('active');
		}
	}

	function handleTimePeriodChange() {
		initializeTokenStatsData();
		createTokenStatsChart();
	}

	afterUpdate(() => {
		if (tokenStatsChartCanvas && tokenStatsLabels.length > 0) {
			createTokenStatsChart();
		}
	});

	onMount(() => {
		initializeTokenStatsData();

		return () => {
			if (tokenStatsChart) tokenStatsChart.destroy();
		};
	});
</script>

<div class="bg-background rounded-lg p-6 shadow-sm">
	<div class="mb-6 flex flex-col justify-between gap-4 sm:flex-row sm:items-center">
		<div>
			<h3 class="text-primary mb-1 text-xl font-semibold">token usage statistics</h3>
			<p class="text-muted-foreground text-sm">daily token consumption by request type</p>
		</div>
		<div class="flex flex-wrap gap-3 text-xs">
			<div class="flex items-center gap-2">
				<div class="h-3 w-3 rounded-sm" style="background: #00d4ff;"></div>
				<span class="text-foreground">input tokens</span>
			</div>
			<div class="flex items-center gap-2">
				<div class="h-3 w-3 rounded-sm" style="background: #ff6b9d;"></div>
				<span class="text-foreground">output tokens</span>
			</div>
			<div class="flex items-center gap-2">
				<div class="h-3 w-3 rounded-sm" style="background: #ff8a50;"></div>
				<span class="text-foreground">vision tokens</span>
			</div>
		</div>
	</div>
	<div class="relative h-96">
		<canvas bind:this={tokenStatsChartCanvas}></canvas>
	</div>
	<div class="mt-6 flex flex-wrap gap-3">
		<button
			class="rounded-lg px-4 py-2 text-sm font-medium text-white transition-all duration-200 hover:-translate-y-0.5 hover:transform hover:shadow-lg"
			style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);"
			onclick={handleRegenerateData}
		>
			generate new data
		</button>
		<button
			class="rounded-lg px-4 py-2 text-sm font-medium text-white transition-all duration-200 hover:-translate-y-0.5 hover:transform hover:shadow-lg"
			style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);"
			onclick={handleToggleView}
		>
			{showPercentage ? 'show absolute' : 'show percentage'}
		</button>
		<div class="bg-card flex items-center gap-3 rounded-lg border px-4 py-2 shadow-sm">
			<label for="time-period-select" class="text-foreground text-sm font-medium"
				>time period:</label
			>
			<select
				id="time-period-select"
				bind:value={timePeriod}
				onchange={handleTimePeriodChange}
				class="border-input bg-background text-foreground rounded border px-3 py-1 text-sm"
			>
				<option value="daily">daily</option>
				<option value="weekly">weekly</option>
				<option value="monthly">monthly</option>
			</select>
		</div>
	</div>
</div>
