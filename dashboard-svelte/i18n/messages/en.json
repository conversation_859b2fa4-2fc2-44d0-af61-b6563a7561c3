{"$schema": "https://inlang.com/schema/inlang-message-format", "page_title": "making your work easier. on.ai for chats and calls", "loading": "loading...", "welcome_to_onai": "welcome to on.ai", "making_business_easier": "making business easier", "phone_number": "phone number", "whatsapp_number": "whatsapp number", "email_address": "email address", "login_method": "login method", "confirmation_code": "confirmation code", "code_sent_to_whatsapp": "code sent to whatsapp", "code_sent_to_email": "code sent to email", "failed_to_send_code": "failed to send code", "successful_authorization": "successful authorization", "failed_to_verify_code": "failed to verify code", "get_code": "get code", "status": "status", "management": "management", "new_onai": "new on.ai", "new_instruction_question": "want to give me a new instruction on another number or instagram page?", "unnamed": "unnamed", "otp_whatsapp_info": "confirmation code sent to whatsapp and valid for 5 minutes", "otp_email_info": "confirmation code sent to email and valid for 5 minutes. if you don't see the email, check your spam folder", "verifying": "verifying...", "confirm": "confirm", "back": "back", "resend_code": "resend code", "resend_timer": "resend available in {seconds} sec", "captcha_required": "please complete the captcha verification", "onai": "on.ai", "deals": "deals", "channels": "channels", "crm_dashboard": "crm dashboard", "error_not_found": "not found 🙈", "error_internal_server": "internal server error 🙈", "error_internal": "internal error 🙈", "error_forbidden": "forbidden 🙈", "error_unauthorized": "unauthorized 🙈", "error_bad_request": "bad request 🙈", "error_gateway_timeout": "gateway timeout 🙈", "error_service_unavailable": "service unavailable 🙈", "error_page_missing": "oops! the page you're looking for has run away or moved.", "error_generic": "oops! something went wrong. please try again later or contact our customer care pro -> whatsapp", "error_default": "page not found", "back_to_home": "back to on.ai", "bot_name": "on.ai name", "active": "active", "setup_fee": "setup fee", "monthly_subscription": "monthly subscription", "logout": "logout", "onai_not_found": "on.ai not found", "page_deleted": "page deleted", "lets_start_with_whatsapp": "let's start with whatsapp", "no_reminders_set": "no reminders set", "no_connected_channels": "no connected channels", "no_stages": "no stages defined. add one below.", "whatsapp_connection": "whatsapp connection", "invalid_email": "please enter a valid email", "onai_successfully_deleted": "on.ai successfully deleted", "onai_name_updated": "name successfully updated", "onai_name_update_failed": "failed to update name", "onai_name_update_failed_no_id": "failed to update name: id not found", "onai_name_placeholder": "enter name", "onai_name_edit": "edit name", "cancel": "cancel", "onai_name_save": "save", "onai_name_unnamed": "unnamed", "onai_delete": "delete on.ai", "onai_delete_confirm": "are you sure you want to delete this on.ai? this action cannot be undone.", "onai_deleting": "deleting...", "onai_instructions": "instructions", "onai_base": "base", "onai_reminder": "reminder", "onai_ping_times_updated": "ping times updated", "onai_edit_base": "edit base", "onai_update_base": "update base for your on.ai", "onai_enter_base": "enter base...", "onai_base_updated": "base successfully updated", "onai_edit_reminder": "edit reminder", "onai_update_reminder": "update reminder for your on.ai", "onai_enter_reminder": "enter reminder...", "onai_reminder_updated": "reminder successfully updated", "onai_status_update_failed_no_id": "failed to update status: id not found", "onai_status_activated": "on.ai activated", "onai_status_deactivated": "on.ai deactivated", "onai_status_update_failed": "failed to update status", "onai_status_off": "off", "add_channel": "add channel", "select_channel": "select channel", "select_channel_description": "select a channel to connect to your on.ai", "close": "close", "connect_channel": "connect channel", "connect_channel_description": "connect your channel to start receiving messages", "whatsapp_connect_description": "connect whatsapp to start communicating with clients through a unified interface", "connect_whatsapp": "connect whatsapp", "channel_whatsapp_description": "let's light up customer chat through whatsapp!", "channel_wazzup_description": "ready for fun and fast communication?", "channel_instagram_description": "dive into stylish customer communication", "channel_waba_description": "powerful tool for large-scale and creative communication", "channel_telegram_description": "connect with customers through secure and fast messaging", "invalid_phone": "please enter a valid phone number", "manage_stages": "manage stages", "stages_saved_success": "stages saved successfully", "stages_invalid_data": "invalid stages data", "deal_add_failed": "failed to add deal", "deal_added_success": "deal \"{name}\" added successfully", "onai_knowledge": "knowledge base", "knowledge_create": "create knowledge", "knowledge_edit": "edit knowledge", "knowledge_create_description": "create new knowledge content", "knowledge_edit_description": "edit your knowledge content and save changes", "knowledge_content": "content", "knowledge_metadata": "metadata", "knowledge_name": "name", "knowledge_description": "description", "knowledge_type": "type", "knowledge_change_description": "change description", "knowledge_change_description_placeholder": "describe your changes (optional)", "history": "history", "knowledge_save": "save", "knowledge_saving": "saving...", "knowledge_created": "knowledge created successfully", "knowledge_updated": "knowledge updated successfully", "knowledge_restored": "content restored", "knowledge_empty_content": "content cannot be empty", "knowledge_empty_name": "name cannot be empty", "history_title": "history", "history_description": "view and compare previous versions", "history_empty": "history is empty", "knowledge_restore": "restore", "knowledge_compare": "compare", "knowledge_version": "version {number}", "knowledge_select_version": "select version", "knowledge_document_file": "document", "knowledge_text_file": "text", "knowledge_upload_document": "upload or update your document", "knowledge_upload_text": "upload or update your text", "knowledge_file": "file", "knowledge_upload_file": "upload file", "knowledge_inline_editor": "inline editor", "knowledge_enter_name": "enter file name", "knowledge_enter_content": "text", "knowledge_download_file": "download file", "knowledge_file_upload_error": "please select a file to upload", "knowledge_inline_content_error": "please enter content", "knowledge_file_upload_success": "file uploaded successfully", "knowledge_file_update_success": "file updated successfully", "knowledge_content_update_success": "content updated successfully", "knowledge_content_save_success": "content saved successfully", "knowledge_download_error": "failed to download file", "knowledge_version_restored": "version restored successfully", "knowledge_delete": "delete", "knowledge_delete_success": "knowledge successfully deleted", "knowledge_delete_error": "failed to delete knowledge", "knowledge_delete_confirm": "are you sure you want to delete this knowledge? this action cannot be undone.", "knowledge_deleting": "deleting...", "knowledge_updating": "update", "knowledge_uploading": "upload", "no_stages_available": "no stages available. click the settings button to create stages.", "channel_chatapp_description": "simple and efficient way to engage with your customers", "channel_enabled": "enabled", "channel_disabled": "disabled", "channel_delete": "delete channel", "channel_name": "channel name", "channel_type": "channel type", "channel_status_enabled": "channel enabled", "channel_status_disabled": "channel disabled", "channel_delete_failed": "failed to delete channel", "channel_status_update_failed": "failed to update channel status", "error_select_channel_type": "please select a channel type", "channel_name_placeholder": "enter channel name", "file_upload_invalid_format": "please select a valid file format", "file_upload_change": "click to change file", "file_upload_drag": "drag & drop your file here", "file_upload_browse": "or click to browse", "knowledge_text": "text", "knowledge_document": "document", "knowledge_type_select": "select knowledge", "knowledge_supported_formats": "supported formats:", "error_oops": "oops! something went wrong. please try again later or contact our customer care pro ->", "whatsapp_contact": "whatsapp", "error_500_code": "500", "error_500_text": "internal error 🙈", "channel_settings_configure": "configure channel settings", "channel_settings_save": "save settings", "channel_settings_saving": "saving...", "channel_settings_success": "settings saved successfully", "channel_settings_failed": "failed to save settings", "channel_settings": "channel settings", "whatsapp_status_connected": "connected 🚀", "whatsapp_status_connecting": "connecting...", "whatsapp_status_error": "error", "whatsapp_status_unknown": "unknown", "whatsapp_create_failed": "failed to create whatsapp channel", "whatsapp_fetch_failed": "failed to fetch whatsapp status", "whatsapp_status_init": "waiting QR...", "initialize_account": "initialize your account", "select_currency_prompt": "please select your preferred currency to continue", "preferred_currency": "preferred currency", "initialize": "initialize", "initialization_failed": "initialization failed", "currency_usd": "USD - US Dollar", "currency_kzt": "KZT - Kazakhstani Tenge", "footer_copyright": "all rights reserved", "footer_public_offer": "public offer", "footer_user_agreement": "user agreement", "balance_minimum_amount": "minimum amount", "balance_management": "balance management", "balance_top_up": "top up", "balance_amount": "amount", "balance_enter_amount": "enter amount", "balance_currency": "currency", "balance_payment_method": "payment method", "balance_freedom_pay": "freedom pay", "balance_airba_pay": "airba pay", "balance_transaction_history": "transaction history", "delete_account": "delete account", "enable_whatsapp_notifications": "enable whatsapp notifications", "manage_account_settings": "manage your account settings and preferences", "receive_email_notifications": "receive email notifications", "request_data": "request data", "save_changes": "save changes", "settings": "settings", "no_funnels_available": "no funnels available. create a funnel to get started.", "funnel_select": "select funnel", "funnel_loading": "loading...", "funnel_new": "new funnel", "funnel_create": "create new funnel", "funnel_edit": "edit funnel", "funnel_name": "name", "funnel_name_placeholder": "sales pipeline", "funnel_description": "description", "funnel_description_placeholder": "optional description for this funnel", "funnel_set_default": "set as default funnel", "funnel_create_button": "create", "funnel_update_button": "update", "funnel_create_success": "funnel created successfully", "funnel_update_success": "funnel updated successfully", "funnel_save_error": "failed to save funnel", "sse_connection_lost": "real-time connection lost. updates may be delayed.", "sse_connection_failed": "failed to establish real-time connection. updates may be delayed.", "stage_manager_title": "manage stages", "stage_manager_description": "customize the stages and colors for your kanban board.", "new_stage": "new stage", "compare_versions": "compare versions", "select_versions_to_compare": "select versions to compare", "version": "version {number}", "select_version": "select version", "restore": "restore", "no_description": "no description", "knowledge_close": "close", "knowledge_show_differences": "show differences", "knowledge_load_error": "failed to load version content", "knowledge_load_error_log": "failed to load {side} version content", "knowledge_show_differences_description": "enable to highlight changes between versions", "knowledge_show_changes": "show changes", "knowledge_show_changes_description": "enable to highlight changes between versions", "no_version_description": "no description", "support_whatsapp_chat": "Hello, I'm {identifier}, and I need help.", "support_whatsapp_chat_no_identifier": "Hello, I need help.", "model_temperature": "model temperature", "model_temperature_description": "adjust the model's creativity level. higher values (closer to 1.0) make responses more creative, while lower values (closer to 0.0) make them more precise.", "model_temperature_updated": "temperature updated successfully", "model_temperature_update_failed": "failed to update temperature", "model_temperature_load_failed": "failed to load model temperature", "dashboard": "dashboard", "users": "users", "clients": "clients", "toggle": "toggle", "no_users_available": "no users available", "prompts": "prompts", "main_prompt": "main guidance prompt", "ping_prompt": "ping guidance prompt", "report_prompt": "report guidance prompt", "accounts": "accounts", "total_accounts": "total accounts", "subscription": "subscription", "plan": "plan", "term": "term", "tokens_remaining": "tokens remaining", "tokens_consumed": "tokens consumed", "channel_slots": "channel slots", "subscription_expires_on": "subscription expires on", "failed_to_load_subscription": "subscription required", "card_payment": "card payment", "days_remaining": "days remaining", "days_consumed": "days consumed", "payment_choose_plan": "choose a plan", "payment_basic_features": "basic features", "payment_advanced_features": "advanced features", "payment_period": "period", "payment_monthly": "monthly", "payment_annually": "annually", "payment_method": "payment method", "payment_total": "total", "payment_per_month": "kzt / month", "payment_per_year": "kzt / year", "payment_proceed_to_payment": "proceed to payment", "select_funnel_first": "please select a funnel first", "payment_open_plan": "open plan", "payment_omni_plan": "omni plan", "payment_basic_ai": "basic ai assistance", "payment_standard_support": "standard support", "payment_advanced_ai": "advanced ai capabilities", "payment_priority_support": "priority support", "payment_custom_integrations": "custom integrations", "payment_custom_duration": "custom duration", "payment_months": "months", "payment_month": "month", "payment_months_for": "months for", "payment_year": "year", "payment_years": "years", "payment_billed_every": "billed every", "payment_save": "save", "payment_order_summary": "order summary", "payment_selected_plan": "selected plan:", "payment_duration": "duration:", "payment_monthly_rate": "monthly rate:", "payment_per_month_rate": "/month", "no_channels_available": "no channels available", "subscription_disabled": "subscription disabled", "users_search": "search", "users_status_enabled": "enabled", "users_status_disabled": "disabled", "users_enable_button": "enable", "users_disable_button": "disable", "users_pings": "pings", "users_reset_button": "reset", "users_reset_messages_button": "clear messages", "users_reset_all_button": "reset all users", "users_reset_all_messages_button": "clear all messages", "users_reset_confirm": "are you sure you want to", "users_reset_success": "reset successful", "users_reset_messages_success": "messages cleared", "users_reset_all_success": "all users reset", "users_reset_all_messages_success": "all messages cleared", "users_reset_dialog_title": "reset confirmation", "users_reset_all_dialog_title": "reset all users", "users_reset_messages_dialog_title": "clear messages", "users_reset_all_messages_dialog_title": "clear all messages", "users_error_enabling": "error enabling user", "users_error_disabling": "error disabling user", "users_error_resetting": "error resetting user", "users_error_clearing_messages": "error clearing user messages", "users_error_resetting_all_messages": "error resetting all messages", "users_error_resetting_all_users": "error resetting all users", "users_error_searching": "error searching users", "users_reset_all_messages_button_short": "reset all msgs", "users_reset_all_button_short": "reset all", "users_reset_messages_button_short": "msgs", "users_reset_button_short": "reset", "users_enable_button_short": "enable", "users_disable_button_short": "disable", "analytics_dashboard_title": "analytics dashboard", "analytics_timeframe": "timeframe:", "analytics_timeframe_all": "all time", "analytics_timeframe_month": "last month", "analytics_timeframe_week": "last week", "analytics_message_activity": "message activity", "analytics_messages_sent": "messages sent", "analytics_estimated_capacity": "estimated capacity", "analytics_total_messages_sent": "total messages sent", "analytics_top_users_by_usage": "top users by usage", "analytics_no_data_available": "no data available", "analytics_detailed_user_data": "detailed user data", "analytics_user_header": "user", "analytics_tokens_header": "tokens", "analytics_cost_header": "cost ({currency})", "analytics_tokens_used_label": "tokens used", "analytics_cost_estimate_label": "cost estimate ({currency})", "analytics": "analytics", "crm": "crm", "add_stage": "add stage", "coming_soon": "coming soon", "stage_manager_type_normal": "standard workflow stage", "stage_manager_type_success": "stage for successful task completion", "stage_manager_type_failure": "stage for rejected or failed tasks", "stage_manager_types_title": "Stage Types:", "total_onai": "total on.ai", "analytics_all_channels": "all channels", "analytics_messages_by_role_title": "messages by role in activity", "analytics_role_assistant": "on.ai", "analytics_role_user": "client", "analytics_role_from_me": "from me", "system_prompt": "system prompt", "user_prompt": "user prompt", "onai_edit_media_description": "edit {media} {role} description", "onai_update_media_description": "update {media} {role} description for your on.ai", "onai_media_description": "{media} {role} description", "onai_enter_media_description": "enter {media} {role} description...", "onai_media_description_updated": "{media} {role} description successfully updated", "onai_edit_document_summary": "edit document {role} summary", "onai_update_document_summary": "update document {role} summary for your on.ai", "onai_document_summary": "document {role} summary", "onai_enter_document_summary": "enter document {role} summary...", "onai_document_summary_updated": "document {role} summary successfully updated", "chat_features": "features", "enable_phrases": "enable phrases", "disable_phrases": "disable phrases", "new_phrase": "new phrase", "phrase_added": "phrase added", "phrase_removed": "phrase removed", "other_crms": "other crms", "onai_edit_report": "edit report", "onai_update_report": "update report for your on.ai", "onai_report": "report", "onai_enter_report": "enter report...", "onai_report_updated": "report successfully updated", "qr_code": "qr code", "onai_prompt_missing_title": "someone forgot to feed the prompt hamster", "onai_prompt_missing_description": "on.ai can't work properly because main or report prompt is missing. please configure both prompts.", "language_detection_system_prompt": "language detection system prompt", "onai_edit_chat_language_detection": "edit chat language detection prompt", "onai_update_chat_language_detection": "update chat language detection prompt for your on.ai", "onai_chat_language_detection": "chat language detection prompt", "onai_enter_chat_language_detection": "enter chat language detection prompt...", "onai_chat_language_detection_updated": "chat language detection prompt successfully updated", "sandbox_mode": "sandbox mode", "sandbox_mode_description": "when enabled, on.ai will only respond to the specified users.", "sandbox_mode_user_identifier": "user identifier", "sandbox_mode_user_identifier_placeholder": "enter phone number or username", "sandbox_mode_enabled": "sandbox mode enabled", "sandbox_mode_disabled": "sandbox mode disabled", "sandbox_mode_updated": "sandbox mode updated", "sandbox_mode_update_failed": "failed to update sandbox mode", "channel_delete_title": "delete channel", "channel_delete_select_reason": "select a reason", "channel_delete_reason_not_needed": "no longer needed", "channel_delete_reason_cannot_login": "can't login/scan QR", "channel_deleting": "deleting...", "symbols": "symbols", "tokens": "tokens", "no_differences_found": "no differences found", "left_version": "left version", "right_version": "right version", "file_upload_selected_files": "selected files ({count})", "file_upload_clear_all": "clear all", "file_upload_processing": "processing files...", "file_upload_progress": "upload progress", "file_upload_completed": "upload completed", "file_upload_failed": "upload failed", "file_upload_already_uploaded": "already uploaded", "file_upload_remove_file": "remove {fileName}", "file_upload_download_file": "download {fileName}", "file_upload_delete_file": "delete {fileName}", "file_upload_files_selected": "{count} file{plural} selected", "file_upload_uploading": "uploading {fileName} ({current}/{total})", "file_upload_upload_success": "{count} file{plural} uploaded successfully", "file_upload_upload_partial": "{success} file{successPlural} uploaded successfully, {failed} failed", "file_upload_upload_failed": "failed to upload {count} file{plural}", "knowledge_supported_formats_label": "supported formats:", "knowledge_save_text_error": "failed to save text", "knowledge_save_document_error": "failed to save document", "knowledge_delete_file_error": "failed to delete file", "system_alerts": "system alerts", "funnel_delete_confirm": "are you sure you want to delete the funnel \"{funnelName}\"? this action cannot be undone.", "funnel_delete_button": "delete funnel", "funnel_deleting": "deleting...", "funnel_delete_success": "funnel \"{funnelName}\" deleted successfully", "processed_messages": "processed messages", "total_processed_messages": "total processed messages", "daily_processed_messages": "daily processed messages over 30 days"}