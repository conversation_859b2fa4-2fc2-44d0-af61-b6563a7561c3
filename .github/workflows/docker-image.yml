name: build-and-publish-docker

on:
  # push:
    # branches: ["main"]
  workflow_dispatch:

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  build:
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write  # required to push to GitHub Container Registry
    steps:
      - name: Checkout source
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2
        with:
          install: true

      - name: Log in to GitHub Container Registry
        uses: docker/login-action@v2
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Build Docker image (unsquashed)
        uses: docker/build-push-action@v5
        with:
          context: .
          platforms: linux/amd64
          load: true
          tags: app-unsquashed-${{ github.sha }}

      - name: Download docker-squash script
        run: |
          curl -sL https://raw.githubusercontent.com/shinsenter/docker-squash/main/docker-squash.sh -o docker-squash.sh
          chmod +x docker-squash.sh

      - name: Generate squash Dockerfile
        run: |
          ./docker-squash.sh --print app-unsquashed-${{ github.sha }} > Dockerfile.squash

      - name: Build squashed Docker images
        env:
          DOCKER_BUILDKIT: 0
        run: |
          docker build -f Dockerfile.squash \
            -t ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:latest \
            -t ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:${{ github.sha }} \
            .

      - name: Remove squash Dockerfile
        run: rm Dockerfile.squash

      - name: Push squashed Docker images
        run: |
          docker push ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:latest
          docker push ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:${{ github.sha }} 