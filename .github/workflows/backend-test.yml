name: Backend test

on:
  push:
  pull_request:

jobs:
  run-tests:
    runs-on: ubuntu-latest
    permissions:
      issues: write
      contents: read

    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 1

      - name: Verify and fix Docker setup
        run: |
          echo "Checking Docker installation..."
          docker --version || (echo "Docker not found" && exit 1)

          echo "Checking Docker daemon status..."
          if ! docker info > /dev/null 2>&1; then
            echo "Docker daemon not accessible, attempting to start..."
            sudo systemctl start docker
            sleep 5
          fi

          echo "Verifying Docker socket..."
          ls -la /var/run/docker.sock || echo "Docker socket not found at default location"

          echo "Testing Docker connectivity..."
          docker info
          docker ps

          echo "Docker is ready"

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
        with:
          install: true

      - name: Run backend tests
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          CI: "true" # Flag to indicate we're in CI environment
        run: |
          chmod +x scripts/backend-test.sh
          scripts/backend-test.sh
