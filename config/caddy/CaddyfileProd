(common) {
	encode zstd gzip
	header {
		Strict-Transport-Security "max-age=31536000;"
		X-XSS-Protection "1; mode=block"
		X-Frame-Options "DENY"
		X-Robots-Tag "none"
		-Server
	}
}

(proxy_settings) {
    lb_retries 50
    lb_try_duration 60s
    lb_try_interval 5s
    header_up X-Forwarded-For {remote_host}
    header_up X-Real-IP {remote_host}
    header_up X-Forwarded-Proto {scheme}
    header_up X-Forwarded-Host {host}
    header_up X-Forwarded-Port {server_port}
}

(cors) {
    @cors_preflight method OPTIONS
    @cors header Origin {args.0}
    handle @cors_preflight {
        header Access-Control-Allow-Origin "{args.0}"
        header Access-Control-Allow-Methods "GET, POST, PUT, PATCH, DELETE, OPTIONS"
        header Access-Control-Allow-Headers "Content-Type, Authorization, X-Requested-With"
        header Access-Control-Max-Age "3600"
        respond "" 204
    }
    handle @cors {
        header Access-Control-Allow-Origin "{args.0}"
        header Access-Control-Expose-Headers "Link"
        header Vary Origin
    }
}

(websocket) {
flush_interval -1
    stream_timeout 24h
    stream_close_delay 10s
}

(posthog_handlers) {
    handle_path /merry-christmas/static* {
        # rewrite * /static/{path}
        rewrite * /static{path}
        reverse_proxy https://eu-assets.i.posthog.com:443 {
            header_up Host eu-assets.i.posthog.com
            header_down -Access-Control-Allow-Origin
        }
    }

    handle_path /merry-christmas* {
        # rewrite * {path}
        uri replace /merry-christmas /
        reverse_proxy https://eu.i.posthog.com:443 {
            header_up Host eu.i.posthog.com
            header_down -Access-Control-Allow-Origin
        }
    }
}

(otel_collector) {
    route /purplish/* {
        uri strip_prefix /purplish
        reverse_proxy otel_collector_on_ai:4318 {
            import proxy_settings
        }
    }
}

app.build-on.ai {
    import common
    import posthog_handlers
    import otel_collector
    route /rest/* {
        import cors https://localhost
        reverse_proxy on_ai_conductor:8080 {
            import proxy_settings
        }
    }
    route /* {
        import cors https://localhost
        reverse_proxy on_ai_frontend:3000 {
            import proxy_settings
            import websocket
        }
    }
    file_server
}
